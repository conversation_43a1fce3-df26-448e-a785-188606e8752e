[![Swift](https://img.shields.io/badge/Swift-5.0-orange?style=flat-square)](https://img.shields.io/badge/Swift-5.0-orange?style=flat-square)
[![Platforms](https://img.shields.io/badge/Platforms-iOS-yellowgreen?style=flat-square)](https://img.shields.io/badge/Platforms-iOS-yellowgreen?style=flat-square)

# RadioLikeMe

A white-label iOS application platform for radio stations, providing live streaming, user engagement features, and location-based content delivery. Built with modern iOS technologies and designed for scalability across multiple radio station brands.

## 🎯 Overview

RadioLikeMe enables radio stations to deliver personalized listening experiences through:
- **Live Audio Streaming** with high-quality playback
- **Location-Based Services** for relevant local content
- **User Engagement** through interactive features and notifications
- **Multi-Target Architecture** supporting dozens of radio station brands
- **Real-time Analytics** and user behavior tracking

## 📋 Table of Contents

- [Requirements](#requirements)
- [Quick Start](#quick-start)
- [System Setup](#system-setup)
- [Project Architecture](#project-architecture)
- [Build & Deploy](#build--deploy)
- [Development Workflow](#development-workflow)
- [Troubleshooting](#troubleshooting)

## 🔧 Requirements

### System Requirements
- **iOS**: 15.0+
- **Xcode**: Latest stable version

### Development Tools
- **Ruby**: 3.2.2 (managed via chruby)
- **Node.js**: 14.x
- **Python**: 3.11+
- **Bundler**: For Ruby gem management

### Access Requirements
Contact the team lead for access to:
- **Apple Developer Account** (for certificates & provisioning)
- **Firebase Projects** (for analytics & distribution)
- **Airship Account** (for push notifications)
- **Private Repositories** (1PlusX SDK, forked dependencies)

## 🚀 Quick Start

```bash
# 1. Clone the repository
git clone [repository-url]
cd RadioLikeMee

# 2. Install dependencies
bundle install

# 3. Setup project for a specific target
bundle exec fastlane setup --env computerBob

# 4. Open in Xcode
open RLM.xcodeproj

# 5. Build and run
```

## 🛠 System Setup

### 1. Shell Environment (Optional but Recommended)
```bash
# Install Oh My Zsh
sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"
```

### 2. Homebrew
```bash
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Configure shell (replace 'username' with your actual username)
echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
eval "$(/opt/homebrew/bin/brew shellenv)"

# Verify installation
brew doctor
```

### 3. Ruby Environment
```bash
# Install Ruby tools
brew install ruby-install chruby

# Install Ruby 3.2.2
ruby-install ruby 3.2.2

# Configure shell
echo 'source /opt/homebrew/opt/chruby/share/chruby/chruby.sh' >> ~/.zshrc
echo 'source /opt/homebrew/opt/chruby/share/chruby/auto.sh' >> ~/.zshrc
echo 'chruby ruby-3.2.2' >> ~/.zshrc

# Restart terminal and verify
ruby -v  # Should show ruby 3.2.2
```

### 4. Python
```bash
# Install Python
brew install python

# Configure PATH
echo 'export PATH="/opt/homebrew/opt/python@3.11/libexec/bin:$PATH"' >> ~/.zshrc

# Verify
python --version  # Should show Python 3.11.x
```

### 5. Node.js
```bash
# Install Node 14
brew install node@14

# Configure PATH
echo 'export PATH="/opt/homebrew/opt/node@14/bin:$PATH"' >> ~/.zshrc

# Verify
node --version  # Should show v14.x.x
```

## 🏗 Project Architecture

### Directory Structure
```
RadioLikeMee/
├── App/
│   ├── Application/          # SwiftUI app entry point
│   ├── DataLayer/           # Data persistence & networking
│   ├── DomainLayer/         # Business logic & models
│   ├── LegacyApp/           # UIKit-based legacy code
│   ├── PresentationLayer/   # UI components & view controllers
│   └── Script/
│       └── wls              # White Label Solution generator
├── Frameworks/              # Custom frameworks
├── NotificationServiceExtension/
├── RLM.xcodeproj/          # Xcode project
├── Targets/                # Target-specific configurations
│   ├── 80s/, 90s/, BRB/    # Radio station configs
│   ├── ComputerBob/        # Example target
│   └── [20+ other targets]
├── fastlane/               # Build automation
│   ├── Fastfile           # Build lanes
│   ├── .env.*             # Environment configs
│   └── Credentials/       # Target-specific secrets
└── Gemfile                # Ruby dependencies
```

### Key Technologies
- **Swift 5.0+** & **SwiftUI/UIKit** - Native iOS development
- **Swift Package Manager** - Dependency management
- **Firebase** - Analytics, crashlytics, app distribution
- **AWS SDK** - Cloud services integration
- **Airship** - Push notifications & user engagement
- **Fastlane** - CI/CD automation

### White Label Solution (WLS)
The `App/Script/wls` binary generates target-specific:
- Constants and configuration files
- Localized strings and resources
- UI assets and branding
- Feature flags and SDK configurations
- Xcode project modifications

## 🚢 Build & Deploy

### Available Targets
```
80s, 90s, BRB, BRF, BigFM, ComputerBob, Delta, FFN, 
KissFM, MyBob, PSR, RPR1, RS2, RSA, RSH, Radio7, 
Regenbogen, RockFM, SPR, Sunshine, Wacken
```

### Fastlane Commands

#### Project Setup
```bash
# Generate target-specific project files
bundle exec fastlane setup --env [TARGET]
```

#### Development Builds
```bash
# Build for testing (Firebase distribution)
bundle exec fastlane preprod --env [TARGET]
```

#### Production Builds
```bash
# Build for App Store
bundle exec fastlane prod --env [TARGET]
```

#### Batch Operations
```bash
# Build all targets
bundle exec fastlane build_all type:preprod
bundle exec fastlane build_all type:prod

# Version management
bundle exec fastlane bump_version type:patch
bundle exec fastlane bump_version type:minor
```

### Build Environments
- **PREPROD**: Ad-hoc distribution via Firebase (QA, developers, clients)
- **APPSTORE**: Production builds for App Store submission

## 🔄 Development Workflow

### Git Flow
We follow **Gitflow** branching strategy:

- **`master`** - Production-ready releases
- **`develop`** - Integration branch for features
- **`feature/*`** - New feature development
- **`release/*`** - Release preparation
- **`hotfix/*`** - Critical production fixes

### Versioning
**Semantic Versioning**: `MAJOR.MINOR.PATCH`
- **MAJOR**: Breaking changes (unique per target)
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Development Process
1. **Create feature branch** from `develop`
2. **Run setup** for your target: `bundle exec fastlane setup --env [TARGET]`
3. **Develop & test** locally
4. **Create pull request** to `develop`
5. **Deploy via Fastlane** after merge

## 🔧 Troubleshooting

### Common Issues

#### WLS Script Permissions
```bash
chmod +x App/Script/wls
```

#### Missing Dependencies
```bash
# Reinstall Ruby gems
bundle install

# Clear and reinstall Node modules (if needed)
rm -rf node_modules && npm install
```

#### Build Failures
1. **Clean build folder**: Product → Clean Build Folder
2. **Reset derived data**: ~/Library/Developer/Xcode/DerivedData
3. **Re-run setup**: `bundle exec fastlane setup --env [TARGET]`

#### Environment Issues
- Verify `.env.[TARGET]` exists in `fastlane/` directory
- Check credentials in `fastlane/Credentials/[TARGET]/`
- Ensure all required environment variables are set

### Debug Commands
```bash
# Verbose WLS output
./App/Script/wls [TARGET] -p ios -x ./RLM.xcodeproj -o ./Targets --verbose

# Fastlane with verbose output
bundle exec fastlane setup --env [TARGET] --verbose
```

## 📦 Dependencies

### Swift Package Manager
Key dependencies managed via SPM:
- **Firebase SDK** (Analytics, Crashlytics, App Distribution)
- **AWS SDK iOS** (Cloud services)
- **Airship SDK** (Push notifications)
- **Reachability.swift** (Network monitoring)
- **Stevia** (Auto Layout DSL)

### Ruby Gems
Managed via `Gemfile`:
- **Fastlane** (Build automation)
- **Xcodeproj** (Xcode project manipulation)

## 🤝 Contributing

1. **Follow Git Flow** workflow
2. **Run setup** before development
3. **Test thoroughly** on your target
4. **Update documentation** if needed
5. **Create descriptive** pull requests

## 📞 Support

- **Technical Issues**: Contact development team
- **Access Requests**: Contact team lead
- **Documentation**: Internal wiki and this README

---

**Note**: This is a white-label solution supporting 20+ radio station brands. Always specify your target environment when running commands.
```