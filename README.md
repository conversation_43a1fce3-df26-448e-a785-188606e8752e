[![Swift](https://img.shields.io/badge/Swift-5.0-orange?style=flat-square)](https://img.shields.io/badge/Swift-5.0-orange?style=flat-square)
[![Platforms](https://img.shields.io/badge/Platforms-iOS-yellowgreen?style=flat-square)](https://img.shields.io/badge/Platforms-iOS-yellowgreen?style=flat-square)
[![Swift Package Manager](https://img.shields.io/badge/Swift_Package_Manager-compatible-orange?style=flat-square)](https://img.shields.io/badge/Swift_Package_Manager-compatible-orange?style=flat-square)

# Radio Like Me

RadioLikeMe is a mobile application designed to enhance user engagement and streamline interaction with live radio streams. Aimed at radio stations, the app offers a variety of features including live streaming, user reviews, and detailed analytics integration. By utilizing advanced location services, RadioLikeMe provides users with relevant content and updates based on their geographic location, ensuring a personalized and localized listening experience.

The project supports multiple targets, catering to various radio stations with unique requirements. It employs Fastlane for continuous integration and deployment, ensuring a seamless and efficient release pipeline. The app's architecture and codebase are continuously updated to incorporate modern iOS capabilities, enhancing performance and maintaining high standards of quality. RadioLikeMe is dedicated to delivering an exceptional user experience, making it a leading solution in the radio streaming app market.


## Table of Contents
* [Requirements](#requirements)
* [System Setup](#system-setup)
    * [Oh my zsh](#oh-my-zsh)
    * [Homebrew](#homebrew)
    * [Ruby](#ruby)
    * [Python](#python)
    * [Bundler](#bundler)
* [Project Setup](#project-setup)
    * [WLS Script](#wls-script)
    * [Running the Setup](#running-the-setup)
    * [Troubleshooting](#troubleshooting)
* [CLI Setup](#cli-setup)
    * [Important](#important)
    * [Environment](#environment-setup)
* [GIT](#git)

## Requirements
- iOS 16.0+
- Xcode

Contact Lasse to invite you to:
- 1PlusX - Private SDK. You need to be invited to this repo in order to fetch it.  - Gitlab (code.the-resc.com) - This is where we have our private forked repos (CRThen, Kommunicate-iOS-SDK, KommunicateChatUI-iOS-SDK)
- Apple developer account
- Bitrise - Client's Bitrise team, not CR one. It's called resc.
- Airship - notification service (for testing)

## System Setup
Prepare your terminal !!!

### Oh my zsh
1. Install Oh-my-zsh
    ```
    sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"
    ```
    **Note:** Your shell is your choice. Setup is pretty much the same. I was writing this as I was going through setup.

2. Restart your terminal

### Homebrew
1. Install Homebrew
   ```
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. After instalation, run the two commands Homebrew is suggesting (make sure you use the correct path, replace computerrock with your username):
    ```
    (echo; echo 'eval "$(/opt/homebrew/bin/brew shellenv)"') >> /Users/<USER>/.zprofile
    ```
    and
    ```
    eval "$(/opt/homebrew/bin/brew shellenv)"
    ```

3. To make sure everything is set correctly, type:
    ```
    brew doctor
    ```
    If everything is ok you'll get the message:
    ```
    Your system is ready to brew
    ```

### Ruby
1. Install ruby and chruby
    ```
    brew install ruby-install chruby
    ```

2. Verify ruby-install
    ```
    ruby-install -V
    ```
    If everything is ok you'll get the version installed:
    ```
    ruby-install: 0.9.2
    ```

3. Check the current version of Ruby
    ```
    ruby -v
    ```
    It should print something like:
    ```
    ruby 2.6.10p210 (2022-04-12 revision 67958) [universal.arm64e-darwin23]
    ```

4. Now that we have Ruby utilities installed, we can install Ruby. To install the latest Ruby type:
    ```
    ruby-install --update ruby
    ```
    To install specific version of Ruby:
    ```
    ruby-install ruby 3.2.2
    ```

5. Open `/.zshrc` with `open -e /.zshrc`, or by typing `zshconfig` if alias is enabled. Add those lines at bottom of your file.
    ```
    source /opt/homebrew/opt/chruby/share/chruby/chruby.sh
    source /opt/homebrew/opt/chruby/share/chruby/auto.sh
    chruby ruby-3.2.2
    ```

5. Save file and restart your terminal.
    If something went wrong you should see an error when opening terminal.

6. After restarting the terminal, check the Ruby version again.
    ```
    ruby -v
    ```
    Now it should print:
    ```
    ruby 3.2.2 (2023-03-30 revision e51014f9c0) [arm64-darwin23]
    ```

### Python
1. Install latest version of Python
    ```
    brew install python
    ```

2. Check python version
    ```
    python --version
    ```
    you should get response:
    ```
    command not found: python
    ```

3. Check python info by typing:
    ```
    brew info python
    ```
    You should get a print like this:
    ```
    Python has been installed as
    /opt/homebrew/bin/python3

    Unversioned symlinks `python`, `python-config`, `pip` etc. pointing to `python3`, `python3-config`, `pip3` etc., respectively, have been installed into /opt/homebrew/opt/python@3.11/libexec/bin
    ```
4. Open `/.zshrc` by `open -e /.zshrc`, or by typing `zshconfig` if alias is enabled.

5. Add the following line at the begging of the file:
    ```
    export PATH="/opt/homebrew/opt/python@3.11/libexec/bin:$PATH"
    ```
    This is the path we got from `brew info python`.

6. Check python version:
    ```
    python --version
    ```
    you should get response:
    ```
    Python 3.11.6
    ```

### Bundler
1. Go to your project dir and type:
```
bundle install
```
This will install fastlane and related tools

### Node
1. Install Node 14
    ```
    brew install node@14
    ```
    In case brew tells you that node 14 is no longer support, head to [nodejs.org][nodejs] and download prebuilt installer for version 14.21.3.

3. Configure shell
    ```
    echo 'export PATH="/opt/homebrew/opt/node@14/bin:$PATH"' >> ~/.zshrc
    ```
    **Note:** Follow installation instructions. In case you installed node via prebuilt package, your path might deffer. In that case, node mighe be in `/usr/local/bin/node/bin`.

## Project Setup

The project has evolved from using design-tools as a submodule to using a custom WLS (White Label Solution) script located at `App/Script/wls`. This script is now used to generate constants, resources, strings, audio files, fonts, add precompiled flags, SPM dependencies, and other project-specific configurations for each radio station target.

### WLS Script

The WLS script is a binary executable that replaces the previous design-tools setup. Each target is defined by dedicated configuration that contains general settings and feature flags per app, all strings used in the app, as well as UI specifications.

### Running the Setup

Running the setup is done through fastlane. In your PROJECT_DIR type:
```
bundle exec fastlane setup --env computerBOB
```
**Note**: Replace `computerBOB` with desired scheme.

Available schemes include:
- 80s, 90s, BRB, BRF, BigFM, ComputerBob, Delta, FFN, KissFM, MyBob, PSR, RPR1, RS2, RSA, RSH, Radio7, Regenbogen, RockFM, SPR, Sunshine, Wacken

The setup lane calls the WLS script with the following parameters:
```
./App/Script/wls [SCHEME] -p ios -x ./RLM.xcodeproj -o ./Targets --verbose
```

### Troubleshooting

If you encounter issues with the WLS script:

1. **Permission Issues**: Make sure the WLS script has execute permissions:
   ```
   chmod +x App/Script/wls
   ```

2. **Missing Dependencies**: Ensure all system dependencies are installed (Ruby, Python, Node.js)

3. **Environment Variables**: Check that your environment file (`.env.[scheme]`) exists in the fastlane directory

4. **Verbose Output**: Use the `--verbose` flag to get detailed output for debugging

## CLI Setup
There is no white label solution on Bitrise (yet). Each app is on it's own which is kinda troublesome, since all apps have the same setup.

### Important
- Only one build can be scheduled. This is due to limitation when reading google sheets in paralel.
- When adding a new device to Provisioning Profiles, we also need to update Profiles on Bitrise

### Environment Setup
- PREPROD
- APPSTORE

Our backend has single environment, so Xcode or in this case Bitrise environment plays no role. What is different for these environments, is SDK environment. Some SDKs has single set of keys, while some have dev + prod keys. PREPROD and APPSTORE will always use production keys. For example, Airship has dev + prod keys, while Crashlytics will be the same for all environments.

## Fastlane Commands

The project uses Fastlane for automation. Available commands:

### Setup
Generate tokens and prepare project:
```
bundle exec fastlane setup --env [SCHEME]
```

### Build for Pre-production
Build for pre-production testing and distribute via Firebase:
```
bundle exec fastlane preprod --env [SCHEME]
```

### Build for Production
Build for production and upload to App Store:
```
bundle exec fastlane prod --env [SCHEME]
```

### Build All Environments
Build all environments at once:
```
bundle exec fastlane build_all type:preprod
bundle exec fastlane build_all type:prod
```

### Version Management
Bump marketing version (minor or patch) for all targets:
```
bundle exec fastlane bump_version type:patch
bundle exec fastlane bump_version type:minor
```

## Architecture

The project follows a modular architecture with the following key components:

- **App/Application**: Main app entry point and SwiftUI app structure
- **App/LegacyApp**: Legacy UIKit-based application code
- **App/DataLayer**: Data management and persistence
- **App/DomainLayer**: Business logic and domain models
- **App/PresentationLayer**: UI components and view controllers
- **Targets**: Configuration files for different radio station targets
- **Frameworks**: Custom frameworks and dependencies

### Key Technologies

- **Swift 5.0+** - Primary programming language
- **SwiftUI & UIKit** - User interface frameworks
- **Swift Package Manager** - Dependency management
- **Fastlane** - Build automation and deployment
- **Firebase** - Analytics, distribution, and backend services
- **AWS SDK** - Cloud services integration
- **Airship** - Push notifications and user engagement
- **Reachability** - Network connectivity monitoring

## GIT

We follow the standard Gitflow workflow to manage our development process. Gitflow is a branching model that helps us keep our project organized and ensures a smooth development lifecycle.

Key branches in our workflow:
- `master`: The production-ready state of the project.
- `develop`: The branch where the latest development changes are integrated.
- `feature/*`: Feature branches created from `develop` for new features.
- `release/*`: Release branches created from `develop` for preparing a new production release.
- `hotfix/*`: Hotfix branches created from `master` for urgent bug fixes in production.

For a detailed overview of Gitflow, refer to the [Gitflow Cheatsheet][git-flow].

### Versioning

We use Semantic Versioning 2.0.0, following the format `MAJOR.MINOR.PATCH`.

- **Current Version:** X.11.5
- **MAJOR Version:** Increased by 1 for significant changes or incompatible API updates. This version is unique for each app.
- **MINOR Version:** Increased by 1 for backward-compatible functionality additions.
- **PATCH Version:** Increased by 1 for backward-compatible bug fixes. Typically built from the `master` branch.

For more details on Semantic Versioning, refer to the [official documentation][semver].

## Project Structure

```
RadioLikeMee/
├── App/
│   ├── Application/           # Main app entry point
│   ├── DataLayer/            # Data management
│   ├── DomainLayer/          # Business logic
│   ├── LegacyApp/            # Legacy UIKit code
│   ├── PresentationLayer/    # UI components
│   └── Script/
│       └── wls               # White Label Solution script
├── Frameworks/               # Custom frameworks
├── NotificationServiceExtension/
├── RLM.xcodeproj/           # Xcode project
├── Targets/                 # Target-specific configurations
│   ├── 80s/
│   ├── 90s/
│   ├── ComputerBob/
│   └── ...                  # Other radio station targets
├── fastlane/                # Build automation
│   ├── Fastfile
│   ├── .env.*               # Environment configurations
│   └── Credentials/         # Target-specific credentials
├── Gemfile                  # Ruby dependencies
└── README.md
```

## Dependencies

The project uses Swift Package Manager for dependency management. Key dependencies include:

- **Firebase SDK** - Analytics, distribution, crashlytics
- **AWS SDK** - Cloud services integration
- **Airship** - Push notifications
- **Reachability.swift** - Network monitoring
- **Stevia** - Auto Layout DSL
- **iCarousel** - Carousel UI component

## Getting Started

1. **Clone the repository**
2. **Install system dependencies** (Ruby, Python, Node.js)
3. **Install Ruby gems**: `bundle install`
4. **Run project setup**: `bundle exec fastlane setup --env [SCHEME]`
5. **Open project in Xcode**: `open RLM.xcodeproj`
6. **Build and run** the project

## Support

For questions or issues, contact the development team or refer to the internal documentation.

---

[nodejs]: https://nodejs.org/en/download/prebuilt-installer
[git-flow]: https://danielkummer.github.io/git-flow-cheatsheet/
[semver]: https://semver.org/