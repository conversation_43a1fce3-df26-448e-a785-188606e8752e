//
//  PersistentInstallRepository.swift
//  radio.likemee
//
//  Created by Computer Rock on 21.12.23..
//  Copyright © 2023 Computer Rock. All rights reserved.
//
//  UUIDRepository is implementaion of various UUID our app is generating. It
//  was mostly created for the Implementation of persistentInstallation
//  feature. This is feature flaged and the flag that tells us if the feature
//  is enabled is in RLMSettings.persistentInstallID. If the flag is on, and
//  we don't have the value already, we should generate new UUID string. Value
//  is never removed, even if the RLMSettings value is false. The only case
//  when UUID is removed is when user uninstalls the app. Since we can't track
//  that, the persistentID is removed in AppDelegate where we can check if
//  the app is freshly installed. If feature is enabled persistentInstallID
//  value is used in every AppsFlyer tracking event, as well as a parameter
//  in URL for every stream.
//

import Foundation
import UserDefaultsProvider
import KeychainProvider

public protocol UUIDRepositoryProtocol {
    func generateUUIDs()
    func readPersistantInstallUUID() -> String?
    func readMetaUUID() -> String?
    func deleteAll()
}

public struct UUIDRepository: UUIDRepositoryProtocol {
    public static let shared = UUIDRepository()
    
    private let defaults: UserDefaultsProvider = SystemUserDefaultsProvider()
    private let keychain: KeychainProvider = SystemKeychainProvider()
    
    private init() {
        migrate()
    }
    
    private func migrate() {
        // slowly migrating of persistent install id
        // if keychain id doesn't exists, do nothing
        guard let keychainID = try? keychain.read(.persistantInstallID) else {
            return
        }
        // defaults id exists, defaults and keychain are the same. do nothing
        if let defaultsID: String = try? defaults.read(.persistantInstallID),
            defaultsID == keychainID {
            return
        }
        // migrate id
        defaults.update(.persistantInstallID, value: keychainID)
        Util.debugLog("Migration successful")
    }
    
    public func generateUUIDs() {
        generatePersistentInstallID()
        generateMetaID()
    }
    
    private func generatePersistentInstallID() {
        // if feature flag is on
        guard RLMSettings.persistentInstallID else {
            return
        }
        
        // if persistantInstallID already exists
        do {
            let persistentID: String = try defaults.read(.persistantInstallID)
            Util.debugLog("PersistentInstallID exists \(persistentID)!")
        } catch UserDefaultsProviderError.valueForKeyNotFound {
            defaults.update(.persistantInstallID, value: UUID().uuidString)
            Util.debugLog("PersistentInstallID created!")
        } catch {
            Util.debugLog("PersistentInstallID NOT created! \(error.localizedDescription)")
        }
    }
    
    private func generateMetaID() {
        // metaID is generated on every app start
        defaults.update(.metaID, value: UUID().uuidString)
    }
    
    public func readPersistantInstallUUID() -> String? {
        // if feature flag is on
        guard RLMSettings.persistentInstallID else {
            return nil
        }
        
        do {
            return try defaults.read(.persistantInstallID)
        } catch {
            Util.debugLog("PersistantInstallID NOT found!")
            return nil
        }
    }
    
    public func readMetaUUID() -> String? {
        do {
            return try defaults.read(.metaID)
        } catch {
            Util.debugLog("MetaID NOT found!")
            return nil
        }
    }
    
    public func deleteAll() {
        do {
            try keychain.deleteAll()
            defaults.deleteAll()
            Util.debugLog("UDIDs deleted")
        } catch {
            Util.debugLog("UDIDs NOT deleted")
        }
    }
}
