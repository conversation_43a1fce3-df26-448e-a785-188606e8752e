//
//  KommunicateConversationViewController.swift
//  radio.likemee
//
//  Created by Computer Rock on 3.2.25..
//  Copyright © 2025 Computer Rock. All rights reserved.
//

#if USE_KOMMUNICATE
import Foundation
import Kommunicate
import KommunicateChatUI_iOS_SDK

public final class KommunicateConversationViewController: KMConversationViewController {
    public required init(
        configuration: ALKConfiguration,
        conversationViewConfiguration: KMConversationViewConfiguration,
        individualLaunch: Bool = true) {
            super.init(
                configuration: configuration,
                conversationViewConfiguration: conversationViewConfiguration,
                individualLaunch: individualLaunch
            )
            
            viewModel = ALKConversationViewModel(
                contactId: nil,
                channelKey: nil,
                localizedStringFileName: nil
            )
    }
    
    override public func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        edgesForExtendedLayout = .bottom
        extendedLayoutIncludesOpaqueBars = true
        
        StickyPlayer.setIsStickyAllowed(false, andShow: true)
    }
    
    public func update(channelKey: NSNumber?) {
        guard viewModel.channelKey != channelKey else {
            return
        }
        
        viewModel.channelKey = channelKey
        refreshViewController()
    }
}
#endif
