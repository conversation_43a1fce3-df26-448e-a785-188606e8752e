//
//  KommunicateRepository.swift
//  computerBOB
//
//  Created by Computer Rock on 17.1.25..
//  Copyright © 2025 Computer Rock. All rights reserved.
//

#if USE_KOMMUNICATE
import UserDefaultsProvider
import Kommunicate
import KommunicateCore_iOS_SDK
import KommunicateChatUI_iOS_SDK

public extension Notification.Name {
    static let rlmTabBarWillAppear = Notification.Name("rlmTabBarWillAppear")
}

public final class KommunicateRepository {
    public static let shared: KommunicateRepository = KommunicateRepository()
    
    private let appID: String = RLMSettings.kommunicateAppId
    private let channelService: ALChannelService = ALChannelService.sharedInstance()
    private let messageService: ALMessageService = ALMessageService.sharedInstance()
    private let pushService: KMPushNotificationService = KMPushNotificationService()
    private let registerUserService: KMRegisterUserClientService = KMRegisterUserClientService()
    private let debugLog: Bool = true
    
    private var tabBarWillAppearObserver: Any?
    private let defaults: UserDefaultsProvider
    
    private var startPageConfig: ResponseObjectMainConfigStartPageConfig? {
        DataManager.shared.startPage.config?.first
    }
    
    private lazy var defaultConfiguration: KMConfiguration = {
        var configuration: KMConfiguration = KMConfiguration()
        configuration.localizedStringFileName = "Kommunicate"
        
        configuration.hideFaqButtonInConversationView = true
        configuration.hideLineImageFromChatBar = true
        configuration.isProfileTapActionEnabled = false
        configuration.isNewSystemPhotosUIEnabled = true
        
        configuration.messageMenuOptions = [.copy]
        
        configuration.chatBar.optionsToShow = .some([.camera, .gallery, .video])
        return configuration
    }()
    
    private lazy var conversationViewConfiguration: KMConversationViewConfiguration = {
        var configuration: KMConversationViewConfiguration = KMConversationViewConfiguration()
        configuration.hideBackButton = true
        configuration.isCSATOptionDisabled = true
        return configuration
    }()
    
    public lazy var conversationVC: KMBaseNavigationViewController = {
        KMBaseNavigationViewController(
            rootViewController: KommunicateConversationViewController(
                configuration: defaultConfiguration,
                conversationViewConfiguration: conversationViewConfiguration
            )
        )
    }()
    
    private init() {
        self.defaults = SystemUserDefaultsProvider()
    }
    
    public func setup() {
        Kommunicate.setup(applicationId: appID)
        
        Kommunicate.defaultConfiguration = defaultConfiguration
        Kommunicate.kmConversationViewConfiguration = conversationViewConfiguration
        
        let kmApplocalNotificationHandler: KMAppLocalNotification = KMAppLocalNotification.appLocalNotificationHandler()
        kmApplocalNotificationHandler.dataConnectionNotificationHandler()
        
        configure()
    }
    
    private func setupNavigationBarAppearance() {
        let navigationBarProxy = UINavigationBar.appearance(
            whenContainedInInstancesOf: [KMBaseNavigationViewController.self]
        )
        navigationBarProxy.barTintColor = RLMColor.backgroundBgConsents.color
    }
    
    private func configure() {
        setupNavigationBarAppearance()
        
        // Received message
        KMMessageStyle.receivedBubble.style = .round
        
        // Sent message
        KMMessageStyle.sentBubble.color = RLMColor.backgroundBgConsents.color
        KMMessageStyle.sentBubble.style = .round

        KMCoreSettings.replyOptionEnabled(false)
    }
    
    public func registerUser(userID: String, email: String?, displayName: String?, completion: @escaping (Bool)->()) {
        guard hasUserConsent(userID: userID) else {
            debugLog("Register - Canceled! User consent not given")
            completion(false)
            return
        }
        
        guard !Kommunicate.isLoggedIn else {
            debugLog("Register - Success! Already logged in")
            completion(true)
            return
        }
        
        let kmUser = KMUser()
        kmUser.applicationId = appID
        kmUser.userId = userID
        kmUser.email = email
        kmUser.displayName = displayName
        
        Kommunicate.registerUser(kmUser) { [weak self] response, error in
            if error != nil {
                self?.debugLog("Register - Failed! Error: \(String(describing: error))")
                completion(false)
            } else {
                self?.debugLog("Register - Success!")
                completion(true)
            }
        }
    }
    
    public func logout() {
        guard Kommunicate.isLoggedIn else {
            debugLog("Logout - Canceled! Not logged in")
            return
        }
        
        Kommunicate.logoutUser { [weak self] result in
            switch result {
            case .success:
                self?.debugLog("Logout - Success!")
            case .failure(let error):
                self?.debugLog("Logout - Failed! Error: \(error)")
            }
            // after logout we need to set appID again
            self?.setup()
        }
    }
    
    public func hasUserConsent(userID: String?) -> Bool {
        do {
            let consents: [String: Bool] = try defaults.read(.chatConsents)
            if let userID, let consent: Bool = consents[userID] {
                return consent
            }
            return false
        } catch {
            return false
        }
    }
    
    public func setConsent(_ consent: Bool, userID: String) {
        var consents: [String: Bool] = [String: Bool]()
        
        if let defaultsConsents: [String: Bool] = try? defaults.read(.chatConsents) {
            consents = defaultsConsents
        }
        
        consents[userID] = consent
        defaults.update(.chatConsents, value: consents)
    }
    
    public func openChat(element: ResponseObjectMainConfigStartPageElement?, completion: @escaping (Bool)->()) {
        guard Kommunicate.isLoggedIn, let userID = KMUserDefaultHandler.getUserId() else {
            debugLog("Open Chat - Canceled! Not logged in!")
            completion(false)
            return
        }
        
        guard let conversationVC = conversationVC.children.first as? KommunicateConversationViewController else {
            debugLog("Open Chat - Canceled! No ConversationVC!")
            completion(false)
            return
        }
        
        getUserChannel(userID: userID) { [weak self, weak conversationVC] channel in
            if let channelKey = channel?.key {
                self?.debugLog("Open Chat - Success!")
                conversationVC?.update(channelKey: channelKey)
                self?.sendProxyMessage(channelKey: channelKey, element: element)
                completion(true)
            } else {
                self?.debugLog("Open Chat - No Channel! Creating new one")
                
                self?.createUserChannel(userID: userID) { [weak self, weak conversationVC] channel in
                    if let channelKey = channel?.key {
                        self?.debugLog("Open Chat - Success!")
                        conversationVC?.update(channelKey: channelKey)
                        self?.sendProxyMessage(channelKey: channelKey, element: element)
                        completion(true)
                    } else {
                        self?.debugLog("Open Chat - Failed! No channel")
                        completion(false)
                    }
                }
            }
        }
    }
}

// Notifications
extension KommunicateRepository {
    public func registerForRemoteNotifications(deviceToken: String) {
        if KMUserDefaultHandler.getApnDeviceToken() != deviceToken {
            registerUserService.updateApnDeviceToken(
                withCompletion: deviceToken,
                withCompletion: { [weak self] (response, error) in
                    self?.debugLog("APN response: \(String(describing: response))")
                }
            )
        } else {
            debugLog("No need to update device token.")
        }
    }
    
    public func saveContext() {
        KMDbHandler.sharedInstance().saveContext()
    }
    
    public func handlePushNotification(_ userInfo: [AnyHashable : Any]) -> Bool {
        if isKommunicateNotification(userInfo) {
            pushService.processPushNotification(userInfo, appState: UIApplication.shared.applicationState)
            return true
        }
        return false
    }
    
    public func isKommunicateNotification(_ userInfo: [AnyHashable : Any]) -> Bool {
        pushService.isKommunicateNotification(userInfo)
    }
    
    public func launchChatFromNotification(contactID: String?, groupID: NSNumber?, conversationID: NSNumber?) {
        guard Kommunicate.isLoggedIn else {
            debugLog("Launch from notification - Canceled! Not logged in")
            return
        }
        
        guard let conversationVC = conversationVC.children.first as? KommunicateConversationViewController else {
            debugLog("Launch from notification - Failed. No ConversationVC!")
            return
        }
        
        // in case app is launched from notification
        if self.conversationVC.tabBarController == nil {
            debugLog("setting up the observer to call openChat")
            
            self.tabBarWillAppearObserver = NotificationCenter.default.addObserver(
                forName: .rlmTabBarWillAppear,
                object: nil,
                queue: .main
            ) { [weak self] (note) in
                self?.debugLog("rlmTabBarWillAppearObserver enter")
                
                conversationVC.update(channelKey: groupID)
                self?.conversationVC.tabBarController?.selectTab(.messenger)
                
                self?.debugLog("removing rlmTabBarWillAppearObserver")
                
                if let observer = self?.tabBarWillAppearObserver {
                    NotificationCenter.default.removeObserver(observer)
                }
                self?.tabBarWillAppearObserver = nil
            }
        } else {
            conversationVC.update(channelKey: groupID)
            self.conversationVC.tabBarController?.selectTab(.messenger)
        }
    }
    
    public func showInAppNotification(value: String, contactID: String?, groupID: NSNumber?, conversationID: NSNumber?) {
        ALUtilityClass.thirdDisplayNotificationTS(
            value,
            andForContactId: contactID,
            withGroupId: groupID,
            completionHandler: { [weak self] _ in
                self?.launchChatFromNotification(
                    contactID: contactID,
                    groupID: groupID,
                    conversationID: conversationID
                )
            }
        )
    }
}

extension KommunicateRepository {
    private func createUserChannel(userID: String, completion: @escaping (ALChannel?)->()) {
        let message = startPageConfig?.kommunicate_general_message ?? "Hallo, wie kann ich helfen?"
        
        var chatAgentArray: [String] = []
        if let chatAgentID = startPageConfig?.chat_agent_id {
            chatAgentArray = [chatAgentID]
        }
        
        let kmConversation = KMConversationBuilder()
            .withClientConversationId(userID)
            .withAgentIds(chatAgentArray)
            .withMetaData([
                "WELCOME_MESSAGE": message
            ])
            .useLastConversation(true)
            .build()
        
        Kommunicate.createConversation(conversation: kmConversation) { [weak self] result in
            switch result {
            case .success(let conversationID):
                self?.debugLog("Create Channel - Success!")
                self?.getUserChannel(userID: conversationID, completion: completion)
            case .failure(let error):
                self?.debugLog("Create Channel - Failed!\nError: \(error)")
                completion(nil)
            }
        }
    }
    
    private func getUserChannel(userID: String, completion: @escaping (ALChannel?)->()) {
        channelService.getChannelInformation(
            nil,
            orClientChannelKey: userID,
            withCompletion: { [weak self] channel in
                if let channel {
                    self?.debugLog("Set User channel - Success!")
                    completion(channel)
                } else {
                    self?.debugLog("Set User channel - Failed!")
                    completion(nil)
                }
            }
        )
    }
    
    private func sendProxyMessage(channelKey: NSNumber, element: ResponseObjectMainConfigStartPageElement?) {
        guard element?.chat_agent_type == "agent" else {
            debugLog("Proxy request - Canceled. Only agent chats are supported at the moment")
            return
        }
        
        guard let message = element?.element_link_target_copy, !message.isEmpty,
              let agentID = element?.chat_agent_id,
              let proxy = startPageConfig?.kommunicate_proxy_url,
              let proxyURL = URL(string: proxy) else {
            debugLog("Proxy request - Canceled. Missing parameters")
            return
        }
        
        let latestMessage = messageService.getLatestMessage(
            forChannel: channelKey,
            excludeChannelOperations: true
        )
        
        guard latestMessage?.message != message else {
            debugLog("Proxy request - Canceled. Message is the same as the latest one")
            return
        }
        
        // set the conversation topic using proxy
        let payload: [String: String] = [
            "groupId": "\(channelKey.stringValue)",
            "message": "\(message)",
            "fromUserName": "\(agentID)"
        ]
        
        var request = URLRequest(url: proxyURL)
        request.httpMethod = "POST"
        request.httpBody = try? JSONSerialization.data(
            withJSONObject: payload,
            options: []
        )
        request.addValue(
            "application/json",
            forHTTPHeaderField: "Content-Type"
        )
        
        let key = channelKey.stringValue + "+X2bMtsh79v5xu5ZW"
        request.addValue(
            key.md5(),
            forHTTPHeaderField: "Kommunicate-Key"
        )
        
        request.addValue(
            channelKey.stringValue,
            forHTTPHeaderField: "Conversation-ID"
        )
        
        if let token = RlmLoginManager.sharedInstance.tokenAccess {
            request.addValue(
                "Bearer \(token)",
                forHTTPHeaderField: "Authorization"
            )
        }
        
        request.addValue(
            RLMSettings.kommunicateApiKey,
            forHTTPHeaderField: "Api-Key"
        )
                                   
        let task = URLSession.shared.dataTask(
            with: request,
            completionHandler: { [weak self] data, response, error -> Void in
                if let response = response {
                    self?.debugLog("Proxy request - Success!")
                    self?.debugLog(response.debugDescription)
                }
                if let error {
                    self?.debugLog("Proxy request - Failed! Error: \(error)")
                }
            }
        )
        task.resume()
    }
}

extension KommunicateRepository {
    func debugLog(_ message: String) {
        if debugLog {
            print("Kommunicate - \(message)")
        }
    }
}
#endif
