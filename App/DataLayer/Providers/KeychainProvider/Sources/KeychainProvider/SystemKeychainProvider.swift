//
//  SystemKeychainProvider.swift
//  
//
//  Created by Computer Rock on 20.12.23..
//

import KeychainSwift

public struct SystemKeychainProvider {
    private let keychain: KeychainSwift = KeychainSwift()
    
    public init() {}
}

extension SystemKeychainProvider: KeychainProvider {
    public func read(_ key: KeychainCoding) throws -> String {
        guard let value = keychain.get(key.rawValue) else {
            throw KeychainProviderError.valueForKeyNotFound
        }
        return value
    }
    
    public func update(_ key: KeychainCoding, value: String) throws {
        if !keychain.set(value, forKey: key.rawValue) {
            throw KeychainProviderError.updateFailed
        }
    }
    
    public func delete(_ key: KeychainCoding) throws {
        if !keychain.delete(key.rawValue) {
            throw KeychainProviderError.deleteFailed
        }
    }
    
    public func deleteAll() throws {
        for key in KeychainCoding.allCases {
            try delete(key)
        }
    }
}
