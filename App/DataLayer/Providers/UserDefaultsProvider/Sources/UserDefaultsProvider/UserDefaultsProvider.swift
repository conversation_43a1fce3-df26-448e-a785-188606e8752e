//
//  UserDefaultsProvider.swift
//  
//
//  Created by Computer Rock on 18.3.24..
//

public enum UserDefaultsCoding: String, CaseIterable {
    case persistantInstallID
    case metaID
    case chatConsents
}

public protocol UserDefaultsProvider {
    func read<T>(_ key: UserDefaultsCoding) throws -> T
    func update<T>(_ key: UserDefaultsCoding, value: T)
    func delete(_ key: UserDefaultsCoding)
    func deleteAll()
}
