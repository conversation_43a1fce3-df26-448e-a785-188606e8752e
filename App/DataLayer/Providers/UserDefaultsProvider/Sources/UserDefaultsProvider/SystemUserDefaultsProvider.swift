//
//  SystemUserDefaultsProvider.swift
//
//
//  Created by Computer Rock on 18.3.24..
//

import Foundation

public struct SystemUserDefaultsProvider {
    private let defaults: UserDefaults = UserDefaults.standard
    
    public init() {}
}

extension SystemUserDefaultsProvider: UserDefaultsProvider {
    public func read<T>(_ key: UserDefaultsCoding) throws -> T {
        guard let object = defaults.object(forKey: key.rawValue) as? T else {
            throw UserDefaultsProviderError.valueForKeyNotFound
        }
        
        return object
    }
    
    public func update<T>(_ key: UserDefaultsCoding, value: T) {
        defaults.set(value, forKey: key.rawValue)
        defaults.synchronize()
    }
    
    public func delete(_ key: UserDefaultsCoding) {
        defaults.removeObject(forKey: key.rawValue)
        defaults.synchronize()
    }
    
    public func deleteAll() {
        for key in UserDefaultsCoding.allCases {
            delete(key)
        }
    }
}
