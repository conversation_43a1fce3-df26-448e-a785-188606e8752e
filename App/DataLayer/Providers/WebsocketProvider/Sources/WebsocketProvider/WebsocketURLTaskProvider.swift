//
//  WebsocketURLTaskProvider.swift
//
//
//  Created by Computer Rock on 15.7.24..
//

import Foundation

public final class WebsocketURLTaskProvider: NSObject, WebsocketProvider {
    private var session: URLSession!
    private let debugLog: Bool = false

    private enum ConnectionState {
        case disconnected
        case connecting(url: URL, task: URLSessionWebSocketTask)
        case connected(url: URL, task: URLSessionWebSocketTask)

        var currentTask: URLSessionWebSocketTask? {
            switch self {
            case .connecting(_, let task), .connected(_, let task):
                return task
            case .disconnected:
                return nil
            }
        }
    }

    private var connectionState: ConnectionState = .disconnected

    public var isConnected: Bool {
        switch connectionState {
        case .connected:
            return true
        default:
            return false
        }
    }

    public weak var delegate: WebsocketProviderDelegate?

    public override init() {
        super.init()
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 5
        self.session = URLSession(
            configuration: configuration,
            delegate: self,
            delegateQueue: OperationQueue()
        )
    }

    deinit {
        disconnect()
        session.invalidateAndCancel()
    }

    public func connect(url: URL) {
        switch connectionState {
        case .connecting(let currentURL, _), .connected(let currentURL, _):
            guard currentURL != url else {
                debugLog("Connection already in progress to \(url)")
                return
            }

            disconnect()
        case .disconnected:
            break
        }

        debugLog("Connecting to \(url)")

        let task = session.webSocketTask(with: url)
        connectionState = .connecting(url: url, task: task)

        startListening()
        task.resume()
    }

    public func disconnect() {
        switch connectionState {
        case .connecting(_, let task), .connected(_, let task):
            task.cancel(with: .normalClosure, reason: nil)
            connectionState = .disconnected
            debugLog("Disconnected")
        case .disconnected:
            debugLog("Already disconnected")
        }
    }

    private func startListening() {
        guard let task = connectionState.currentTask else { return }

        task.receive { [weak self] result in
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self?.debugLog("Received text message")
                    DispatchQueue.main.async {
                        self?.delegate?.receivedMessage(text: text)
                    }
                case .data(let data):
                    self?.debugLog("Received data: \(data.count) bytes")
                @unknown default:
                    self?.debugLog("Unknown message type")
                }

                self?.startListening()
            case .failure(let error):
                self?.debugLog("Listen failed: \(error)")
                break
            }
        }
    }

    private func debugLog(_ message: String) {
        if debugLog {
            print("WebSocket: \(message)")
        }
    }
}

extension WebsocketURLTaskProvider: URLSessionWebSocketDelegate {
    public func urlSession(
        _ session: URLSession,
        webSocketTask: URLSessionWebSocketTask,
        didOpenWithProtocol protocol: String?
    ) {
        debugLog("Connection opened")

        if case .connecting(let url, let task) = connectionState {
            connectionState = .connected(url: url, task: task)
        }

        DispatchQueue.main.async {
            self.delegate?.onConnected()
        }
    }

    public func urlSession(
        _ session: URLSession,
        webSocketTask: URLSessionWebSocketTask,
        didCloseWith closeCode: URLSessionWebSocketTask.CloseCode,
        reason: Data?
    ) {
        debugLog("Connection closed with code: \(closeCode)")
        connectionState = .disconnected

        DispatchQueue.main.async {
            self.delegate?.onDisconnected()
        }
    }

    public func urlSession(
        _ session: URLSession,
        task: URLSessionTask,
        didCompleteWithError error: (any Error)?
    ) {
        if let error = error {
            debugLog("Task completed with error: \(error)")
            connectionState = .disconnected

            if let urlError = error as? URLError {
                DispatchQueue.main.async {
                    self.delegate?.onError(error: urlError)
                }
            }
        }
    }
}
