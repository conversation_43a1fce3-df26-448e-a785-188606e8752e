//
//  KeyboardAvoidingHandler.swift
//  computerBOB
//
//  Created by <PERSON> on 16/11/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit

class KeyboardAvoidingHandler {
    private let keyboardDistancer: CGFloat = 16

    // default value from keyboard apperience, it will be updated first time when keyboard appears if needed
    private(set) var keyboardAnimationOptions = UIView.AnimationOptions(rawValue: 7)
    private(set) var keyboardHeight: CGFloat = UIScreen.main.bounds.height * 0.3

    private var lastOffset: CGPoint = CGPoint.zero
    private var scrollViews: [UIScrollView] = [UIScrollView]()
    private var tapGesture: UITapGestureRecognizer?
    private var view: UIView

    private var scrollView: UIScrollView? {
        didSet {
            if let tmpTapGesture =  tapGesture {
                scrollView?.addGestureRecognizer(tmpTapGesture)
            }
        }
    }

    fileprivate var uxParentView: UXView? {
        return view.parentViewController?.view as? UXView
    }

    init(inView: UIView) {
        view = inView
    }

    func didMoveToWindow(window: UIWindow?) {
        if window == nil {
            if let tmpTapGesture =  tapGesture {
                scrollView?.removeGestureRecognizer(tmpTapGesture)
            }

            NotificationCenter.default.removeObserver(
                self,
                name: UIResponder.keyboardWillShowNotification,
                object: nil
            )
            NotificationCenter.default.removeObserver(
                self,
                name: UIResponder.keyboardWillHideNotification,
                object: nil
            )
        } else {
            if scrollViews.count == 0 {
                findTopMostScrollView(in: view)
                tapGesture = UITapGestureRecognizer(
                    target: self,
                    action: #selector(bgTapped)
                )
                tapGesture?.numberOfTapsRequired = 1
                tapGesture?.cancelsTouchesInView = false
            }

            NotificationCenter.default.addObserver(
                self,
                selector: #selector(keyboardWillShow),
                name: UIResponder.keyboardWillShowNotification,
                object: nil
            )
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(keyboardWillHide),
                name: UIResponder.keyboardWillHideNotification,
                object: nil
            )
        }
    }

    @objc private func bgTapped() {
        view.parentViewController?.view.endEditing(true)
    }

    @objc private func keyboardWillShow(notification: Notification) {
        guard view.isFirstResponder else {
            return
        }

        for tmpScrollView in scrollViews {
            if scrollView == nil ||
                (tmpScrollView.frame.size.width >= tmpScrollView.contentSize.width &&
                 tmpScrollView.frame.size.height < tmpScrollView.contentSize.height) {
                scrollView = tmpScrollView
                tmpScrollView.isScrollEnabled = true
            } else {
                tmpScrollView.isScrollEnabled = false
            }
        }

        if let userInfo: NSDictionary = (notification as NSNotification).userInfo as NSDictionary? {
            // update view controller default values
            if let tmpCurve = userInfo[UIResponder.keyboardAnimationCurveUserInfoKey] as? UInt {
                let options = UIView.AnimationOptions(rawValue: tmpCurve)
                keyboardAnimationOptions = options
            }

            if let keyboardSize = (userInfo[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
                let tmp: CGFloat = view.parentViewController?.tabBarController?.tabBar.frame.size.height ?? 0
                keyboardHeight = keyboardSize.height - tmp + keyboardDistancer

                if uxParentView?.scrollViewContentInset == nil {
                    uxParentView?.scrollViewContentInset = scrollView?.contentInset ?? UIEdgeInsets.zero
                }
                lastOffset = scrollView?.contentOffset ?? CGPoint.zero

                scrollToActiveTextfieldIfNeeded()
            }
        }
    }

    @objc private func keyboardWillHide(notification: Notification) {
        if let sv = scrollView {
            let tmpMaxOffset = sv.contentSize.height - sv.bounds.size.height
            + (uxParentView?.scrollViewContentInset?.bottom ?? 0)
            lastOffset.y = min(max(lastOffset.y, 0), max(tmpMaxOffset, 0))
        }

        UIView.animate(withDuration: 0.3, animations: {
            self.scrollView?.contentInset = self.uxParentView?.scrollViewContentInset ?? UIEdgeInsets.zero
            self.scrollView?.contentOffset = self.lastOffset
        }, completion: { _ in
            self.scrollView = nil
        })

        for sv in scrollViews {
            sv.isScrollEnabled = true
        }
    }

    private func scrollToActiveTextfieldIfNeeded() {
        guard view.isFirstResponder, let tmpSV = scrollView else {
            return
        }

        let frameTopY = view.convert(
            view.frame.origin,
            to: view.parentViewController?.view
        ).y + view.frame.height
        let keyboardTopPos = tmpSV.frame.size.height - keyboardHeight

        if frameTopY > keyboardTopPos {
            // move if keyboard hide input field
            let distanceToBottom = tmpSV.frame.size.height - frameTopY
            let collapseSpace = keyboardHeight - distanceToBottom

            if collapseSpace < 0 {
                return
            }

            // set new offset for scroll view
            UIView.animate(
                withDuration: 0.3,
                animations: {
                    // scroll to the position above keyboard 10 points
                    self.scrollView?.contentInset = UIEdgeInsets(
                        top: 0,
                        left: 0,
                        bottom: self.keyboardHeight,
                        right: 0
                    )
                    self.scrollView?.scrollRectToVisible(
                        self.view.frame,
                        animated: false
                    )
                }
            )
        } else {
            scrollView?.contentInset = UIEdgeInsets(
                top: 0,
                left: 0,
                bottom: self.keyboardHeight,
                right: 0
            )
        }
    }

    private func findTopMostScrollView(in view: UIView) {
        if let tmpScroll = view as? UIScrollView {
            if tmpScroll.isScrollEnabled {
                scrollViews.append(tmpScroll)
            }
        }

        guard let tmpParent = view.superview else {
            return
        }

        findTopMostScrollView(in: tmpParent)
    }
}
