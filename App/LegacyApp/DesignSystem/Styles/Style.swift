//
//  Style.swift
//
//
//  Created by <PERSON> on 04/05/2020.
//  Copyright © 2020 ComputerRock. All rights reserved.
//

import UIKit

/// Hashable wrapper for a metatype value.
struct HashableType<T>: Hashable {
    let base: T.Type

    init(_ base: T.Type) {
        self.base = base
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(base))
    }

    static func == (lhs: HashableType, rhs: HashableType) -> Bool {
        return lhs.base == rhs.base
    }
}

extension Dictionary {
    subscript<T>(key: T.Type) -> Value? where Key == HashableType<T> {
        get {
            self[HashableType(key)]
        }
        set {
            self[HashableType(key)] = newValue
        }
    }
}

/// Represents view style with a closure that configures the view.
public struct RlmStyle<View: UIView> {
    public let style: (View) -> Void

    public init(style: @escaping (View) -> Void) {
        self.style = style
    }

    /// Applies self to the view.
    public func apply(to view: View) {
        style(view)

        if let uxc = view as? UXComponent {
            uxc.isStyleApplied = true
        }
    }

    /// Style that does nothing (keeps the default/native style).
    public static var native: RlmStyle<View> {
        return RlmStyle { _ in }
    }

    /// Marges two styles together.
    public func adding<V>(_ other: RlmStyle<V>) -> RlmStyle {
        return RlmStyle {
            self.apply(to: $0)
            other.apply(to: $0 as! V)
        }
    }

    /// Returns current style modified by the given closure.
    public func extend(_ style: @escaping (View) -> Void) -> RlmStyle {
        return RlmStyle {
            self.apply(to: $0)
            style($0)
        }
    }

    /// Modify current style in place.
    public mutating func modify(_ style: @escaping (View) -> Void) {
        self = extend(style)
    }
}

extension UIView {
    /// Init with a given style.
    /// For example: `let nameLabel = UILabel(style: Style.Label.body)`.
    public convenience init<V>(style: RlmStyle<V>) {
        self.init(frame: .zero)
        self.style(style)
    }

    /// Utility function to get style by the given name.
    /// IB stylename property will use this functions to apply the style by name.
    /// Override in your class to support this feature.
    func style<V>(named name: String) -> RlmStyle<V>? {
        return nil
    }

    /// Applies the given style to self and cals out the closure
    @discardableResult
    public func style<V>(_ style: RlmStyle<V>) -> Self {
        guard let view = self as? V else {
            Util.debugLog("💥 Could not apply style for \(V.self) to \(type(of: self))")
            return self
        }

        style.apply(to: view)

        return self
    }

    /// Applies the given style to self and cals out the closure and returns self
    @discardableResult
    public func style<V>(_ style: RlmStyle<V>, _ completion: (V) -> Void ) -> Self {
        guard let view = self as? V else {
            Util.debugLog("💥 Could not apply style for \(V.self) to \(type(of: self))")
            return self
        }

        style.apply(to: view)
        completion(view)

        return self
    }

    /// Styling manager
    private static var _styling: [HashableType<UIView>: AnyObject] = [:]

    static func styling<V>() -> Styling<V> {
        if let styling = _styling[Self.self] as? Styling<V> {
            return styling
        }

        let s: Styling<V> = Styling()
        _styling[Self.self] = s

        return s
    }

    /// Get default style if set by calling .styling().setStyle()
    static func getDefaultStyle<V>() -> RlmStyle<V>? {
        guard let styling = _styling[Self.self] as? Styling<V> else {
            return nil
        }

        return styling.style
    }

    /// Get style when contained in specific view whith a fallback option to provided default value
    static func getStyle<V>(
        whenContainedIn type: UIView.Type,
        defaultTo: RlmStyle<V>? = nil
    ) -> RlmStyle<V>? {
        guard let styling = _styling[Self.self] as? Styling<V> else {
            return nil
        }

        if let style = styling.style(whenContainedIn: type) {
            return style
        }

        return defaultTo
    }
}

public class Styling<View: UIView> {
    var style: RlmStyle<View>?
    private var styles: [HashableType<UIView>: RlmStyle<View>] = [:]

    func setStyle(_ style: RlmStyle<View>, whenContainedIn types: [UIView.Type] = []) {
        if types.isEmpty {
            self.style = style
        } else {
            for type in types {
                styles[HashableType(type)] = style
            }
        }
    }

    func style(whenContainedIn type: UIView.Type) -> RlmStyle<View>? {
        if let style = styles[HashableType(type)] {
            return style
        }

        return nil
    }
}
