//
//  TextStyle.swift
//  
//
//  Created by <PERSON> on 04/05/2020.
//  Copyright © 2020 ComputerRock. All rights reserved.
//

import UIKit

/// Represents text style with a closure that configures the attributes.
public struct UITextStyle {
    public let style: (inout [NSAttributedString.Key: Any]) -> Void

    public init(style: @escaping (inout [NSAttributedString.Key: Any]) -> Void) {
        self.style = style
    }

    /// Applies self to the text.
    public func apply(to text: String?) -> NSAttributedString {
        var attributes: [NSAttributedString.Key: Any] = [:]
        style(&attributes)
        return NSAttributedString(string: text ?? "", attributes: attributes)
    }

    public func extend(_ style: @escaping (inout [NSAttributedString.Key: Any]) -> Void) -> UITextStyle {
        return UITextStyle {
            self.style(&$0)
            style(&$0)
        }
    }

    public func attributes() -> [NSAttributedString.Key: Any] {
        var attributes: [NSAttributedString.Key: Any] = [:]
        style(&attributes)
        return attributes
    }

    var color: UIColor? {
        var attributes: [NSAttributedString.Key: Any] = [:]
        style(&attributes)
        return attributes[NSAttributedString.Key.foregroundColor] as? UIColor
    }

    var font: UIFont? {
        var attributes: [NSAttributedString.Key: Any] = [:]
        style(&attributes)
        return attributes[NSAttributedString.Key.font] as? UIFont
    }

    var fontSize: CGFloat? {
        return font?.pointSize
    }
}

extension String {
    func style(_ style: UITextStyle) -> NSAttributedString {
        return style.apply(to: self)
    }

    public func style(
        _ style: UITextStyle,
        _ modifier: (NSAttributedString) -> NSAttributedString
    ) -> NSAttributedString {
        return modifier(style.apply(to: self))
    }
}
