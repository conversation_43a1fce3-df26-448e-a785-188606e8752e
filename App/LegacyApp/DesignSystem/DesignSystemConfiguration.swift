//
//  DesignSystemConfiguration.swift
//  
//
//  Created by <PERSON> on 30/05/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

protocol DesignSystemConfigurationProtocol {
    func configure()
}

extension DesignSystemConfigurationProtocol {
    // Implement this function in your application extension of DesigSystem
    // Default implementation
    func configure() {
        // textfield default styles per view
        let vcs = [
            RegisterView.self,
            LoginView.self,
            EmailView.self,
            ForgotPasswordView.self,
            VerifyView.self,
            ContactFeedbackView.self
        ]
        
        UXTextField.styling().setStyle(
            UXTextField.Styles.inputField,
            whenContainedIn: vcs
        )
        
        UXTextField.styling().setStyle(
            UXTextField.Styles.inputFieldProfile,
            whenContainedIn: [ProfileView.self]
        )
    }
}

public struct DesignSystem: DesignSystemConfigurationProtocol {
    static var shared = DesignSystem()
    
    private init() {}
}
