//
//  UXAccordionView.swift
//  radio.likemee
//
//  Created by <PERSON> on 5/26/22.
//  Copyright © 2022 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXAccordionView: UXView, ValidatableField {
    fileprivate var stack: UIStackView = {
        let x = UIStackView()
        x.axis = .vertical
        x.spacing = RLMHeight.spacerS
        return x
    }()
    
    let openCloseButton = UXButton(
        text: "    ",
        withStyle: UXButton.Styles.accordionButton,
        false
    )
    
    let arrowImage: UXButton = {
        let v = UXButton()
        v.setImage(RLMImage.togglerArrowDown.image, for: .normal)
        v.contentMode = .center
        return v
    }()
    
    private var _onSelectionChanged: ((_ choiceView: UXAccordionView, _ selected: Int, _ option: RadioOption) -> Void)?
    
    func onSelectionChanged(
        _ f: @escaping ((_ view: UXAccordionView, _ selected: Int, _ option: RadioOption) -> Void)
    ) {
        _onSelectionChanged = f
    }

    private var _onOpenClose: ((_ choiceView: UXAccordionView) -> Void)?
    func onOpenClose(_ f: @escaping ((_ view: UXAccordionView) -> Void)) {
        _onOpenClose = f
    }
    
    var isMandatory: Bool = false
    
    func select(_ selected: Int, execute: Bool = false) {
        guard selected >= 0, selected < self.options.count else {
            return
        }
        
        self.selected = selected
        self.openCloseButton.setTitleText(self.options[selected].label)
        
        if execute {
            _onSelectionChanged?(self, selected, self.options[selected])
        }
        
        self.toggle()
        
        for i in 0..<self.views.count {
            self.views[i].isSelected = (i == self.selected)
        }
    }
    
    fileprivate var options = [RadioOption]()
    fileprivate var views = [UXButton]()
    fileprivate var selected: Int?

    convenience init(
        items: [RadioOption],
        selected: Int? = nil,
        placeholder: String = ""
    ) {
        self.init()
        self.options = items
        
        if let sel = selected, sel >= 0, sel < self.options.count {
            self.selected = sel
        }
        
        self.createItems()
        self.openCloseButton.setTitleText(placeholder)
        self.stack.spacing = 0
    }
    
    fileprivate func createItems() {
        var a = [UXButton]()
        
        for i in 0..<self.options.count {
            let btn = UXButton(
                text: options[i].label,
                withStyle: UXButton.Styles.accordionOption,
                false
            )
            btn.Height == 50
            btn.onPress { btn in
                self.select(i, execute: true)
            }
            a.append(btn)
        }
        
        self.views = a
    }
    
    override func handleRefresh() {
        super.handleRefresh()
        
        for v in self.stack.arrangedSubviews {
            self.stack.removeArrangedSubview(v)
            v.removeFromSuperview()
        }
        
        guard self.isOpen else {
            return
        }
        
        for i in 0..<self.views.count {
            self.views[i].isSelected = (i == self.selected)
            stack.addArrangedSubview(self.views[i])
        }
    }
    
    override func configure() {
        super.configure()
        
        subviews {
            stack
            openCloseButton
            arrowImage
        }
        
        layout {
            0
            |-0-openCloseButton-0-arrowImage-0-| ~ 50
            0
            |-0-stack-0-|
            0
        }
        
        self.backgroundColor = RLMColor.accordionBackground.color
        
        arrowImage.Width == 50
        
    }
    
    var isOpen: Bool = false
    
    fileprivate func toggle() {
        self.isOpen.toggle()
        self.configure()
        
        UIView.animate(
            withDuration: 0.3,
            delay: 0,
            options: .beginFromCurrentState
        ) {
            self.arrowImage.transform = CGAffineTransform(
                rotationAngle: self.isOpen ? .pi : 0
            )
        } completion: { done in
            if let sel = self.selected {
                if self.isOpen {
                    self.openCloseButton.setTitleText("")
                } else {
                    self.openCloseButton.setTitleText(self.options[sel].label)
                }
            }
        }
        
        self.refresh(animated: false) { v in
            self._onOpenClose?(self)
        }
    }

    override func defineActions() {
        self.arrowImage.onPress { btn in
            self.toggle()
        }
        
        self.openCloseButton.onPress { btn in
            self.toggle()
        }
    }

    
    private var validator: ((_ checkbox: UXAccordionView, _ value: Int?) -> ValidationError?)? = nil
    
    var validationErrorMessage: String?
    /// DO NOT CALL FROM VALIDATOR IT SELF
    @discardableResult
    func validate(
        with validator: @escaping (_ checkbox: UXAccordionView, _ value: Int?) -> ValidationError?
    ) -> UXAccordionView {
        self.validator = validator
        return self
    }
  
    /// Validate the input and changes the state of the control
    @discardableResult
    func validate() -> Bool {
        guard let error_message = validator?(self,self.selected)?.message else {
            return true
        }
        
        validationErrorMessage = error_message
        return false
    }
    
    var isError: Bool = false {
        didSet {
            if isError {
                self.openCloseButton.style(UXButton.Styles.accordionButtonError)
            } else {
                self.openCloseButton.style(UXButton.Styles.accordionButton)
            }
            self.refresh()
        }
    }
}
