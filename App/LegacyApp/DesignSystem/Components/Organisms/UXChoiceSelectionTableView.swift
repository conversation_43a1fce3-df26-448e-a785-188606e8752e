//
//  UXChoiceSelectionTableView.swift
//  radio.likemee
//
//  Created by <PERSON> on 25/12/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXChoiceSelectionTableView: UXView, UITableViewDelegate, UITableViewDataSource {
    private let backgroundImage = UIImageView()
    private let tableView = UITableView()

    private var selectedElements: [Int] = []

    private var selected: [Int] = []

    var multipleSelection: Bool = false {
        didSet {
            tableView.allowsMultipleSelection = multipleSelection
        }
    }
    
    var subtitle: String? {
        didSet {
            tableView.reloadSections([0], with: .top)
        }
    }

    convenience init() {
        self.init(frame: .zero)
    }

    convenience init(title: String) {
        self.init()
        self.title = title.localized()
    }

    convenience init(title: String, subtitle: String) {
        self.init(title: title)
        self.subtitle = description
        tableView.reloadSections([0], with: .top)
    }
    
    override func configure() {
        super.configure()

        tableView.register(
            UXChoiceTableViewCell.self,
            forCellReuseIdentifier: "choice"
        )
        tableView.register(
            UXTextTableViewCell.self,
            forCellReuseIdentifier: "description"
        )

        backgroundImage.image = RLMImage.background.image
        backgroundImage.contentMode = UIView.ContentMode.scaleAspectFill
        tableView.backgroundColor = UIColor.clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        
        subviews {
            backgroundImage
            tableView
        }
        
        layout {
            0
            |tableView|
            0
        }

        backgroundImage.setRLMFullScreenImageViewContraints()

        tableView.contentInset = UIEdgeInsets.init(
            top: spacer,
            left: 0,
            bottom: 0,
            right: 0
        )
    }

    override func setTopAnchorFollowSafeAreaLayoutGuide() {
        tableView.Top == safeAreaLayoutGuide.Top
    }
    
    private var spacer = RLMHeight.spacerL
        
    override func handleRefresh() {
        super.handleRefresh()
    }

    private var _numberOfChoices: (() -> (Int))?
    
    @discardableResult
    func numberOfChoices(
        _ handler: @escaping (() -> (Int))
    ) -> UXChoiceSelectionTableView {
        self._numberOfChoices = handler
        return self
    }

    private var _choiceAtIndex: ((UXChoiceView?, Int) -> (UXChoiceView))?
    
    @discardableResult
    func choiceAtIndex(
        _ handler: @escaping ((UXChoiceView?, Int) -> (UXChoiceView))
    ) -> UXChoiceSelectionTableView {
        self._choiceAtIndex = handler
        return self
    }

    private var _onSelectionChanged: (([Int]) -> (Bool))?
    
    @discardableResult
    func onSelectionChanged(
        _ handler: @escaping (([Int]) -> (Bool))
    ) -> UXChoiceSelectionTableView {
        self._onSelectionChanged = handler
        return self
    }
    
    func deselectAll() {
        selected.forEach { index in
            tableView.deselectRow(
                at: IndexPath(
                    row: index,
                    section: 1
                ),
                animated: false
            )
        }
        
        selected = []
    }
    
    func deselect(_ indices: [Int]) {
        indices.forEach { index in
            tableView.deselectRow(
                at: IndexPath(row: index, section: 1),
                animated: false
            )
        }
        
        selected = selected.filter { !indices.contains($0) }
        selected = selected.sorted()
    }

    func select(_ indices: [Int]) {
        if self.multipleSelection == false {
            deselectAll()
        }
        
        selected = indices.sorted()
        selected.forEach { index in
            tableView.selectRow(
                at: IndexPath(row: index, section: 1),
                animated: false,
                scrollPosition: .none
            )
        }
    }

    @discardableResult
    func setSelected(_ indices: [Int]) -> UXChoiceSelectionTableView {
        deselectAll()
        
        selected = indices.sorted()
        selected.forEach { index in
            tableView.selectRow(
                at: IndexPath(row: index, section: 1),
                animated: false,
                scrollPosition: .none
            )
        }
        
        return self
    }
    
    func getSelected() -> [Int] {
        return selected
    }
    
    func numberOfSections(in _: UITableView) -> Int {
        return 2
    }

    func tableView(
        _ tableView: UITableView,
        numberOfRowsInSection section: Int
    ) -> Int {
        if section == 0 {
            return subtitle?.isEmpty == false ? 1 : 0
        }
        
        return _numberOfChoices?() ?? 0
    }
    
    func tableView(
        _ tableView: UITableView,
        cellForRowAt indexPath: IndexPath
    ) -> UITableViewCell {
        if indexPath.section == 0,
           let cell = tableView.dequeueReusableCell(withIdentifier: "description", for: indexPath) as? UXTextTableViewCell {
            cell.setTitle(subtitle).setStyle(UXLabel.Styles.mainCopyText)
        } else if indexPath.section == 1, let cell = tableView.dequeueReusableCell(withIdentifier: "choice", for: indexPath) as? UXChoiceTableViewCell {
            if let choiceView = _choiceAtIndex?(cell.choiceView, indexPath.row) {
                cell.setup(with: choiceView, spacing: spacer)
                
                choiceView.tag = indexPath.row
                choiceView.onSelectionChanged {[weak self] (choiceView, selected) in
                    guard let this = self else {
                        return
                    }
                    
                    if self?.multipleSelection == false {
                        choiceView.setSelected(true, execute: false)
                    }
                    
                    this.select([choiceView.tag])
                    
                    if this._onSelectionChanged?(this.selected) == true {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            this.parentViewController?.navigationController?.popViewController(animated: true)
                        }
                    }
                }
            }
            
            return cell
        }
        
        // dummy cell
        let cell = UITableViewCell()
        cell.backgroundColor = UIColor.clear
        return cell
    }
    
    func tableView(
        _ tableView: UITableView,
        heightForRowAt indexPath: IndexPath
    ) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(
        _ tableView: UITableView,
        didSelectRowAt indexPath: IndexPath
    ) {
        select([indexPath.row])
        
        if _onSelectionChanged?(selected) == true {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.parentViewController?.navigationController?.popViewController(animated: true)
            }
        }
    }
}
