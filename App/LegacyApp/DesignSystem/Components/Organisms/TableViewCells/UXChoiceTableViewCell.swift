//
//  UXChoiceTableViewCell.swift
//  RLM
//
//  Created by Computer Rock on 6. 6. 2025..
//

import UIKit
import Stevia

class UXChoiceTableViewCell: UITableViewCell {
    var choiceView: UXChoiceView?
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        backgroundColor = UIColor.clear
        selectionStyle = .none
    }
    
    override var isSelected: Bool {
        didSet {
            choiceView?.setSelected(isSelected, execute: false)
        }
    }

    func setup(with choice: UXChoiceView, spacing: CGFloat) {
        if choice.title != choiceView?.title {
            choiceView?.removeFromSuperview()
        }
        
        let mL: CGFloat = RLMInsets.margins.leading
        let mR: CGFloat = RLMInsets.margins.trailing

        subviews {
            choice
        }

        layout {
            0
            |-mL-choice-mR-|
            spacing
        }
        
        choiceView = choice
    }
}
