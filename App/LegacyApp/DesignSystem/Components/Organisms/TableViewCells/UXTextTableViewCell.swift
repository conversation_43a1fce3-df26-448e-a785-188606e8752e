//
//  UXTextTableViewCell.swift
//  radio.likemee
//
//  Created by <PERSON> on 31/05/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXTextTableViewCell: UITableViewCell {
    let label = UXLabel()

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)

        let mL: CGFloat = RLMInsets.margins.leading
        let mR: CGFloat = RLMInsets.margins.trailing

        subviews {
            label
        }

        layout {
            0
            |-mL-label-mR-|
            25
        }
        
        backgroundColor = UIColor.clear
        label.style(UXLabel.Styles.pcDescriptionText) {
            $0.numberOfLines = 0
        }
        selectionStyle = .none
    }
    
    @discardableResult
    func setStyle(_ style: RlmStyle<UXLabel>) -> UXTextTableViewCell {
        label.style(style) {
            $0.numberOfLines = 0
        }
        return self
    }

    @discardableResult
    func setBottomSpace(_ spacer: CGFloat) -> UXTextTableViewCell {
        label.bottomConstraint?.constant = spacer
        return self
    }

    @discardableResult
    func setTitle(_ text: String?) -> UXTextTableViewCell {
        self.label.setText(text ?? " ")
        return self
    }
    
    func setTitle(_ text: String?, spacing: CGFloat) {
        self.label.setText(text ?? " ")
    }
}
