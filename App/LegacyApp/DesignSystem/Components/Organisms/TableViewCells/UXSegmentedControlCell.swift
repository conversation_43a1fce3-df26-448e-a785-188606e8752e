//
//  PodcastsSeriesTabCell.swift
//  radio.likemee
//
//  Created by <PERSON> on 31/05/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXSegmentedControlCell: UITableViewCell {
    let segmentedControl = UXSegmentedControl()

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        subviews {
            segmentedControl
        }
        
        layout {
            0
            |-20-segmentedControl-20-| ~ 35
            25
        }
        
        backgroundColor = UIColor.clear
        segmentedControl.style(UXSegmentedControl.Styles.podcasts)
        selectionStyle = .none
    }
    
    var segments: [String] = [] {
        didSet {
            segmentedControl.removeAllSegments()
            
            for (i, title) in segments.enumerated() {
                segmentedControl.insertSegment(
                    withTitle: title,
                    at: i,
                    animated: false
                )
            }
            
            segmentedControl.selectedSegmentIndex = 0
            localize()
        }
    }
    
    var selectedSegmentIndex: Int {
        set {
            segmentedControl.selectedSegmentIndex = newValue
        }
        get {
            return segmentedControl.selectedSegmentIndex
        }
    }
    
    func onChanged(_ handler: @escaping ((UXSegmentedControl) -> Void)) {
        segmentedControl.onChanged(handler)
    }
}
