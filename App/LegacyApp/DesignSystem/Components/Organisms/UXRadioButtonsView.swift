//
//  UXRadioButtonsView.swift
//  radio.likemee
//
//  Created by <PERSON> on 5/20/22.
//  Copyright © 2022 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

struct RadioOption: Codable {
    let key: String?
    let label: String
}

class UXRadioButtonsView: UXView, ValidatableField {
    fileprivate var stack: UIStackView = {
        let x = UIStackView()
        x.axis = .vertical
        x.spacing = RLMHeight.spacerS
        return x
    }()

    var isMandatory: Bool = false
    
    private var _onSelectionChanged: ((_ choiceView: UXRadioButtonsView, _ selected: Int, _ option: RadioOption) -> Void)?

    func onSelectionChanged(
        _ f: @escaping ((_ view: UXRadioButtonsView, _ selected: Int, _ option: RadioOption) -> Void)
    ) {
        _onSelectionChanged = f
    }

    func select(_ selected: Int, execute: Bool = false) {
        guard selected >= 0, selected < self.options.count else {
            return
        }
        
        self.selected = selected
        
        if execute {
            _onSelectionChanged?(self, selected, self.options[selected])
        }
        
        for i in 0..<self.views.count {
            self.views[i].setSelected(i == self.selected, execute: false)
        }
    }
    
    fileprivate var options = [RadioOption]()
    fileprivate var views = [UXCheckbox]()
    fileprivate var selected: Int?

    convenience init(items: [RadioOption], selected: Int? = nil) {
        self.init()
        self.options = items
        
        if let sel = selected, sel >= 0, sel < self.options.count {
            self.selected = sel
        }
        
        self.createItems()
    }
    
    fileprivate func createItems() {
        var a = [UXCheckbox]()
        
        for i in 0..<self.options.count {
            let checkbox = UXCheckbox(
                title: options[i].label,
                buttonStyle: UXButton.Styles.radioButton,
                textStyle: UITextStyles.mainCopyTextLeft,
                linkStyle: nil
            )
            
            checkbox.onSelectionChanged { chk, state in
                self.select(i, execute: true)
            }
            
            checkbox.onLinkTapped { url in
                if url.scheme == "https" && UIApplication.shared.canOpenURL(url) {
                    UIApplication.shared.open(
                        url,
                        options: [:],
                        completionHandler: nil
                    )
                }
            }
            
            a.append(checkbox)
        }
        
        self.views = a
    }
    
    override func handleRefresh() {
        super.handleRefresh()
        
        for v in self.stack.arrangedSubviews {
            self.stack.removeArrangedSubview(v)
        }
        
        for i in 0..<self.views.count {
            self.views[i].setSelected(i == self.selected, execute: false)
            stack.addArrangedSubview(self.views[i])
        }
    }
    
    override func configure() {
        super.configure()
        
        subviews {
            stack
        }
        
        layout {
            0
            |-0-stack-0-|
            0
        }
    }

    
    private var validator: ((_ checkbox: UXRadioButtonsView, _ value: Int?) -> ValidationError?)? = nil
    
    var validationErrorMessage: String?
    /// DO NOT CALL FROM VALIDATOR IT SELF
    @discardableResult
    func validate(
        with validator: @escaping (_  checkbox: UXRadioButtonsView, _ value: Int?) -> ValidationError?
    ) -> UXRadioButtonsView {
        self.validator = validator
        return self
    }
  
    /// Validate the input and changes the state of the control
    @discardableResult
    func validate() -> Bool {
        guard let error_message = validator?(self,self.selected)?.message else {
            return true
        }
        
        validationErrorMessage = error_message
        return false
    }
    
    var isError: Bool = false {
        didSet {
            for chk in self.views {
                if isError {
                    chk.setStyles(
                        buttonStyle: UXButton.Styles.radioButtonError,
                        textStyle: UITextStyles.inputErrorText,
                        linkStyle: nil
                    )
                } else {
                    chk.setStyles(
                        buttonStyle: UXButton.Styles.radioButton,
                        textStyle: UITextStyles.mainCopyTextLeft,
                        linkStyle: nil
                    )
                }
            }
            self.refresh()
        }
    }
}
