//
//  UXChoiceSelectionView.swift
//  radio.likemee
//
//  Created by <PERSON> on 26/11/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

protocol UXChoiceView: UXView {
    var isSelected: Bool { get }

    /// The onSelectionChanged handler is only called when user action lead to status change,
    /// the handler will not be called when UXChoiceSelectionView changes selection
    func onSelectionChanged(
        _ f: @escaping ((_ choiceView: UXChoiceView, _ selected: Bool) -> Void)
    )
    /// setSelected will call onSelectionChanged handler only when execute is set to true
    func setSelected(_ selected: Bool, execute: Bool)
}

class UXChoiceSelectionView: UXVStackView {
    private let lbDescription = UXLabel(
        text: " ",
        withStyle: UXLabel.Styles.mainCopyText
    )
    private var selectedElements = [UXChoiceView]()
    private var elementViews = [UXChoiceView]()

    // override var isScrollingEnabled: Bool { return false }
    convenience init() {
        self.init(frame: .zero)
        lbDescription.isEnabled = false
    }

    convenience init(title: String) {
        self.init()
        self.title = title.localized()
    }

    convenience init(title: String, description: String) {
        self.init(title: title)
        lbDescription.setText(description)
        lbDescription.isEnabled = true
    }

    var multipleSelection: Bool = false

    override func configure() {
        super.configure()

        backgroundImage.image = RLMImage.background.image
        backgroundImage.contentMode = UIView.ContentMode.scaleAspectFill

        lbDescription.numberOfLines = 0
    }

    private var spacer = RLMHeight.spacerL

    @discardableResult
    func setup(
        title: String,
        description: String? = nil,
        selected: [String]? = nil,
        spacer: CGFloat = RLMHeight.spacerL,
        callback: () -> [UXChoiceView] = { [] }
    ) -> UXChoiceSelectionView {

        self.title = title.localized()
        self.spacer = spacer
        if let description = description?.localized() {
            lbDescription.setText(description)
            lbDescription.isEnabled = true
        } else {
            lbDescription.isEnabled = false
        }

        elementViews.removeAll()

        let elements = callback()
        let count = _numberOfChoices?() ?? elements.count

        for index in 0...count-1 {
            let choiceView = _choiceAtIndex?(index) ?? elements[index]
            elementViews.append(choiceView)

            choiceView.tag = index
            choiceView.onSelectionChanged {[weak self] (choiceView, selected) in
                guard let this = self else {
                    return
                }
                if this.multipleSelection == false {
                    let selectedViews = this.elementViews.filter {
                        $0.isSelected == true
                    }

                    for viewToDeselect in selectedViews {
                        viewToDeselect.setSelected(false, execute: false)
                    }
                    choiceView.setSelected(true, execute: false)
                }

                let selectedViews = this.elementViews.filter {
                    $0.isSelected == true
                }

                if this._onSelectionChanged?(selectedViews) == true {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        this.parentViewController?.navigationController?.popViewController(
                            animated: true
                        )
                    }
                }
            }
        }

        if let selected = selected {
            setSelected(selected)
        }

        return self
    }

    override func handleRefresh() {
        super.handleRefresh()

        pushSpacer(RLMHeight.spacerS)
        if lbDescription.isEnabled {
            pushView(lbDescription, padding: 20)
            pushSpacer(spacer)
        }

        for choiceView in elementViews {
            // disable or enable this view?
            choiceView.alpha = choiceView.isUserInteractionEnabled ? 1.0 : 0.4

            pushView(choiceView, padding: 20)
            pushSpacer(spacer)
        }

        localize()
    }

    private var _numberOfChoices: (() -> (Int))?

    @discardableResult
    func numberOfChoices(
        _ handler: @escaping (() -> (Int))
    ) -> UXChoiceSelectionView {
        self._numberOfChoices = handler
        return self
    }

    private var _choiceAtIndex: ((Int) -> (UXChoiceView))?

    @discardableResult
    func choiceAtIndex(
        _ handler: @escaping ((Int) -> (UXChoiceView))
    ) -> UXChoiceSelectionView {
        self._choiceAtIndex = handler
        return self
    }

    private var _onSelectionChanged: (([UXChoiceView]) -> (Bool))?

    @discardableResult
    func onSelectionChanged(
        _ handler: @escaping (([UXChoiceView]) -> (Bool))
    ) -> UXChoiceSelectionView {
        self._onSelectionChanged = handler
        return self
    }

    @discardableResult
    func setSelected(_ titles: [String]) -> UXChoiceSelectionView {
        if self.multipleSelection == false {
            let selectedViews = self.elementViews.filter {
                $0.isSelected == true
            }
            for viewToDeselect in selectedViews {
                viewToDeselect.setSelected(false, execute: false)
            }
        }

        for choiceView in elementViews {
            if let title = choiceView.title, titles.contains(title) {
                choiceView.setSelected(true, execute: false)
            }
        }

        return self
    }
}
