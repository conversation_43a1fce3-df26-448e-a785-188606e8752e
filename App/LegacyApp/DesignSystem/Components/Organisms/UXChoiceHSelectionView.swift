//
//  UXChoiceHSelectionView.swift
//  radio.likemee
//
//  Created by <PERSON> on 16/12/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXChoiceHSelectionControl: UIView {
    public struct Choice {
        var image: UIImage? = nil
        var selectedImage: UIImage? = nil
        var title: String?
        var accessibilityIdentifier: String? = nil
    }
    
    var normalStateAttributes: UXButton.StateAttributes = UXButton.StateStyles.dayBoxInactive
    var selectedStateAttributes: UXButton.StateAttributes = UXButton.StateStyles.dayBoxActive
    
    private let titleLabel = UXLabel()
    private let containerView = UIView()
    
    private var choices: [Choice] = []
    private var buttons: [UXButton] = []
    private var selected: [Int] = []
    
    var multipleSelection: Bool = false
    
    func setTitle(title: String) {
        titleLabel.setText(title)
    }
    
    convenience init(
        title: String,
        _ choices: [Choice],
        preferedHeight: CGFloat = RLMHeight.standard
    ) {
        self.init(frame: .zero)
        
        guard choices.count > 1 else {
            Util.debugLog("Buttons array must contain at-least 2 elements!")
            return
        }
        
        titleLabel.numberOfLines = 0
        titleLabel.setText(title)
        titleLabel.style(UXLabel.Styles.subtitleTextBackground)
        
        self.choices = choices
        
        for button in choices {
            let btn = UXButton(
                title: button.title,
                withStyle: UXButton.Styles.dayBox,
                preferedHeight: preferedHeight
            )
            
            if let image = button.image {//}?.withRenderingMode(.alwaysTemplate) {
                btn.setImage(image, for: .normal)
                btn.centered = true
            }
            
            btn.setTitle(button.title, for: .normal)
            btn.addTarget(
                self,
                action: #selector(onButtonTap(sender:)),
                for: .touchDown
            )
            btn.isAccessibilityElement = true
            btn.accessibilityIdentifier = button.accessibilityIdentifier
            
            self.buttons.append(btn)
        }
        
        // layout buttons
        let spacing = self.buttons.count > 2 ? 4 : 6
        
        containerView.subviews(self.buttons)
        
        var prev: UXButton?
        
        for btn in self.buttons {
            btn.top(0).bottom(0)
            
            if let prev = prev {
                prev.Right == btn.Left - spacing
            }
            
            prev = btn
        }
        
        if let first = self.buttons.first {
            first.left(0).centerVertically()
        }
        
        if let last = self.buttons.last {
            last.right(0)
        }
        
        align(horizontally: self.buttons)
        equal(widths: self.buttons)
        
        // set default controll state
        setSelected([])
        
        // arrange subviews
        subviews {
            titleLabel
            containerView
        }
        
        layout (
            0,
            |-0-titleLabel-(>=0)-|,
            RLMHeight.spacerXSM,
            |-0-containerView-0-|,
            0
        )
    }
    
    @objc private func onButtonTap(sender: UXButton) {
        if let index = buttons.firstIndex(of: sender) {
            let isSelected = selected.contains(index)
            
            if multipleSelection, isSelected {
                deselect([index])
                _onSelectionChanged?(selected)
            } else if !isSelected {
                select([index])
                _onSelectionChanged?(selected)
            }
        }
    }
    
    private var _onSelectionChanged: (([Int]) -> Void)?
    
    @discardableResult
    func onSelectionChanged(_ handler: @escaping (([Int]) -> Void)) -> UXChoiceHSelectionControl {
        self._onSelectionChanged = handler
        return self
    }
    
    func deselectAll() {
        for (i, btn) in buttons.enumerated() {
            btn.setStateAttributes(normalStateAttributes)
            
            if let image = choices[i].image {
                btn.setImage(image, for: .normal)
            }
        }
        
        selected = []
    }
    
    func deselect(_ indices: [Int]) {
        for (i, btn) in buttons.enumerated() {
            if indices.contains(i) {
                btn.setStateAttributes(normalStateAttributes)
                
                if let image = choices[i].selectedImage ?? choices[i].image {
                    btn.setImage(image, for: .normal)
                }
                
                selected = selected.filter { $0 != i }
            }
        }
        
        selected = selected.sorted()
    }
    
    func select(_ indices: [Int]) {
        if self.multipleSelection == false {
            for (i, btn) in buttons.enumerated() {
                if indices.contains(i) {
                    btn.setStateAttributes(selectedStateAttributes)
                    
                    if let image = choices[i].selectedImage ?? choices[i].image {
                        btn.setImage(image, for: .normal)
                    }
                } else {
                    btn.setStateAttributes(normalStateAttributes)
                    
                    if let image = choices[i].image {
                        btn.setImage(image, for: .normal)
                    }
                }
            }
            
            selected = indices
        } else {
            for (i, btn) in buttons.enumerated() {
                if indices.contains(i) {
                    btn.setStateAttributes(selectedStateAttributes)
                    
                    if let image = choices[i].selectedImage ?? choices[i].image {
                        btn.setImage(image, for: .normal)
                    }
                    
                    selected.append(i)
                }
            }
        }
        
        selected = selected.sorted()
    }
    
    func setSelected(_ indices: [Int]) {
        for (i, btn) in buttons.enumerated() {
            if indices.contains(i) {
                btn.setStateAttributes(selectedStateAttributes)
                
                if let image = choices[i].selectedImage ?? choices[i].image {
                    btn.setImage(image, for: .normal)
                }
            } else {
                btn.setStateAttributes(normalStateAttributes)
                
                if let image = choices[i].image {
                    btn.setImage(image, for: .normal)
                }
            }
        }
        
        selected = indices.sorted()
    }
    
    func getSelected() -> [Int] {
        return selected
    }
}
