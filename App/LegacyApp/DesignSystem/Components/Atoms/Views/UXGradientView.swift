//
//  UXGradientView.swift
//  radio.likemee
//
//  Created by <PERSON> on 31/05/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit

public class UXGradientView: UIView {
    public var startColor: UIColor = .black {
        didSet { updateColors() }
    }
    public var endColor: UIColor = .white {
        didSet { updateColors() }
    }
    public var startLocation: Double = 0.0 {
        didSet { updateLocations() }
    }
    public var endLocation: Double = 1.0 {
        didSet { updateLocations() }
    }
    public var angle: CGFloat = 270 {
        didSet { updatePoints() }
    }

    override public class var layerClass: AnyClass {
        CAGradientLayer.self
    }

    var gradientLayer: CAGradientLayer {
        layer as! CAGradientLayer
    }

    var passtrough: Bool = true

    override public func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        let view = super.hitTest(point, with: event)
        return passtrough && view == self ? nil : view
    }

    // create vector pointing in direction of angle
    private func gradientPointsForAngle(_ angle: CGFloat) -> (CGPoint, CGPoint) {
        // get vector start and end points
        let end = pointForAngle(angle)
        let start = oppositePoint(end)

        // convert to gradient space
        let p0 = transformToGradientSpace(start)
        let p1 = transformToGradientSpace(end)
        return (p0, p1)
    }

    private func pointForAngle(_ angle: CGFloat) -> CGPoint {
        // convert degrees to radians
        let radians = angle * .pi / 180.0
        var x = cos(radians)
        var y = sin(radians)

        // (x,y) is in terms unit circle. Extrapolate to unit square to get full vector length
        if (abs(x) > abs(y)) {
            // extrapolate x to unit length
            x = x > 0 ? 1 : -1
            y = x * tan(radians)
        } else {
            // extrapolate y to unit length
            y = y > 0 ? 1 : -1
            x = y / tan(radians)
        }

        return CGPoint(x: x, y: y)
    }

    private func oppositePoint(_ point: CGPoint) -> CGPoint {
        return CGPoint(x: -point.x, y: -point.y)
    }

    private func transformToGradientSpace(_ point: CGPoint) -> CGPoint {
        // input point is in signed unit space: (-1,-1) to (1,1)
        // convert to gradient space: (0,0) to (1,1), with flipped Y axis
        return CGPoint(
            x: (point.x + 1) * 0.5,
            y: 1.0 - (point.y + 1) * 0.5
        )
    }

    func updatePoints() {
        let (start, end) = gradientPointsForAngle(angle)
        gradientLayer.startPoint = start
        gradientLayer.endPoint = end
    }

    func updateLocations() {
        gradientLayer.locations = [
            startLocation as NSNumber,
            endLocation as NSNumber
        ]
    }

    func updateColors() {
        gradientLayer.colors = [
            startColor.cgColor,
            endColor.cgColor
        ]
    }

    override public func layoutSubviews() {
        super.layoutSubviews()
        updatePoints()
        updateLocations()
        updateColors()
    }
}

public class UXGradientMaskView: UXGradientView {
    override public class var layerClass: AnyClass {
        CALayer.self
    }

    override var gradientLayer: CAGradientLayer {
        if let mask = layer.mask as? CAGradientLayer {
            return mask
        }

        let gl = CAGradientLayer()
        layer.mask = gl
        return gl
    }

    override public var bounds: CGRect {
        didSet {
            gradientLayer.frame = bounds
        }
    }

    override public func layoutSubviews() {
        super.layoutSubviews()
        gradientLayer.frame = bounds
    }
}
