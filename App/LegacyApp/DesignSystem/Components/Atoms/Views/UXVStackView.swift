//
//  UXVStackView.swift
//  
//
//  Created by <PERSON> on 24/05/2020.
//  Copyright © 2020 ComputerRock. All rights reserved.
//

import UIKit
import Stevia

protocol UXStackViewDataSourceDelegate: AnyObject {
    func stackView(reloadViewsFor stackView: UXVStackView)
}

class UXVStackView: UXView {
    let scrollView = UXScrollView()
    let backgroundImage = UIImageView()
    let contentView = UXView()

    // MARK: Properties
    var isScrollingEnabled: Bool {
        return true
    }

    /// default padding is UIEdgeInsets.Tokens.margins.left
    var padding: CGFloat = RLMInsets.margins.leading {
        didSet {
            refresh()
        }
    }

    /// Vertical spacing between views, default spacing is 0.
    /// If you need different spacing betweeen views the please keep the default spacing (0)
    /// and insert vertical spacers using pushSpacer(height)
    var spacing: CGFloat = 0 {
        didSet {
            refresh()
        }
    }

    // MARK: Configuere
    private(set) var arrangedViews: [UIView] = []

    // append a view and return previous
    private func addArrangedSubview(_ view: UIView) -> UIView? {
        let last = arrangedViews.last
        arrangedViews.append(view)
        contentView.addSubview(view)
        return last
    }

    func removeAllArrangedSubviews() {
        // Remove the views from contentView
        contentView.subviews.forEach({ $0.removeFromSuperview() })
        arrangedViews.removeAll()
        contentView.removeConstraints(contentView.userAddedConstraints)
    }

    override func configure() {
        super.configure()

        subviews {
            backgroundImage
            scrollView.subviews {
                contentView
            }
        }

        layout {
            0
            |-0-scrollView-0-|
            0
        }

        scrollView.layout {
            0
            |-0-contentView-0-|
            0
        }

        backgroundImage.setRLMFullScreenImageViewContraints()

        scrollView.fillContainer()
        equal(widths: contentView, scrollView)
        contentView.centerHorizontally()

        if isScrollingEnabled == false {
            equal(heights: contentView, scrollView)
        }

        scrollView.contentInsetAdjustmentBehavior = .always
        contentView.applyDefaultStyle()

        backgroundImage.contentMode = UIView.ContentMode.scaleAspectFill
        backgroundColor = .clear
        scrollView.backgroundColor = .clear
        contentView.backgroundColor = .clear

    }

    override func setTopAnchorFollowSafeAreaLayoutGuide() {
        (scrollView.Top == safeAreaLayoutGuide.Top).priority = UILayoutPriority(rawValue: 1000.0)
    }

    // MARK: Data
    weak var dataSource: UXStackViewDataSourceDelegate? {
        didSet {
            refresh()
        }
    }

    private var contentOffsetBeforeRefresh: CGPoint?

    override func handleRefresh() {
        super.handleRefresh()

        // remember scroll offset
        contentOffsetBeforeRefresh = scrollView.contentOffset

        removeAllArrangedSubviews()

        if let dataSource = dataSource {
            dataSource.stackView(reloadViewsFor: self)
        }
    }

    override func afterRefresh() {
        guard let contentOffsetBeforeRefresh = contentOffsetBeforeRefresh else {
            return
        }

        scrollView.setContentOffset(contentOffsetBeforeRefresh, animated: false)
        self.contentOffsetBeforeRefresh = nil
    }

    // MARK: Content insets
    override func setContentInsets(_ insets: UIEdgeInsets) {
        scrollView.contentInset = insets
    }

    // MARK: Arranged Subviews
    private func appendView(_ view: UIView) {
        if let prev = addArrangedSubview(view) {
            if let bc = prev.bottomConstraint {
                contentView.removeConstraint(bc)
            }
            view.Top == (prev.Bottom + spacing)
        } else {
            view.top(0)
        }

        if view.bottomConstraint != nil {
            view.bottomConstraint?.constant = 0
        } else {
            view.bottom(0)
        }
    }

    func pushView(_ view: UIView?, padding: CGFloat? = nil, height: CGFloat? = nil) {
        guard let view = view else {
            return
        }

        view.translatesAutoresizingMaskIntoConstraints = false
        if let h = height {
            view.setHeight(h)
        }
        appendView(view)
        view.fillHorizontally(padding: padding ?? self.padding)
    }

    func pushSpacer(_ space: CGFloat) {
        let view = UIView()
        view.tag = 999
        view.translatesAutoresizingMaskIntoConstraints = false
        view.height(space)

        appendView(view)
        view.fillHorizontally(padding: padding)
    }

    func spacer(_ space: CGFloat) -> UIView {
        let view = UIView()
        view.tag = 999
        view.translatesAutoresizingMaskIntoConstraints = false
        view.height(space)
        return view
    }

    func pushSpacer(_ space: SteviaFlexibleMargin) {
        let view = UIView()
        view.tag = 999
        view.translatesAutoresizingMaskIntoConstraints = false
        view.height(space)

        appendView(view)
        view.fillHorizontally(padding: padding)
    }

    func scrollToView(view: UIView, animated: Bool) {
        scrollView.scrollToView(view: view, animated: animated)
    }

    /// Scroll to top
    func scrollToTop(animated: Bool) {
        scrollView.scrollToTop(animated: animated)
    }

    /// Scroll to bottom
    func scrollToBottom(animated: Bool) {
        scrollView.scrollToBottom(animated: animated)
    }
}
