//
//  UXLabelSizingView.swift
//  radio.likemee
//
//  Created by Computer Rock on 13.5.24..
//  Copyright © 2024 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXLabelSizingView: UXView {
    private let label: UXLabel
    
    init(label: UXLabel) {
        self.label = label
        super.init(frame: CGRect.zero)
        
        configure()
    }
    
    required init?(coder: NSCoder) {
        self.label = UXLabel()
        super.init(coder: coder)
        configure()
    }
    
    override func configure() {
        super.configure()
        
        subviews {
            label
        }
        
        layout {
            label
                .top(0).bottom(0)
                .leading(0).trailing(>=0)
        }
    }
}
