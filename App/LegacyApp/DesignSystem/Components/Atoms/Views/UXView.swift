//
//  UXView.swift
//
//
//  Created by <PERSON> on 24/05/2020.
//  Copyright © 2020 ComputerRock. All rights reserved.
//

import UIKit
import Stevia

extension UIImageView {
    func setRLMFullScreenImageViewContraints() {
        self.top(0).leading(0).trailing(0)
        self.height(UIScreen.main.bounds.width * 812/375)
    }
}

extension UIView {
    var parentViewController: UIViewController? {
        var parentResponder: UIResponder? = self

        while parentResponder != nil {
            parentResponder = parentResponder!.next

            if let viewController = parentResponder as? UIViewController {
                return viewController
            }
        }
        return nil
    }

    var uxTextFieldsInView: [UXTextField] {
        return subviews
            .filter({ !($0 is UXTextField || $0 is UXTextFieldDatePicker) })
            .reduce(( subviews.compactMap { $0 as? UXTextField }), { summ, current in
                return summ + current.uxTextFieldsInView
            })
    }

    var uxValidatableFields: [ValidatableField] {
        return subviews
            .filter({ !($0 is ValidatableField) })
            .reduce(( subviews.compactMap { $0 as? ValidatableField }), { summ, current in
                return summ + current.uxValidatableFields
            })
    }

    var textFieldsInView: [UITextField] {
        return subviews
            .filter({ !($0 is UITextField) })
            .reduce(( subviews.compactMap { $0 as? UITextField }), { summ, current in
                return summ + current.textFieldsInView
            })
    }

    var selectedTextField: UITextField? {
        return textFieldsInView.filter { $0.isFirstResponder }.first
    }

    func invalidateConstraintsAndLayout() {
        setNeedsUpdateConstraints()
        updateConstraintsIfNeeded()
        setNeedsLayout()
        layoutIfNeeded()
    }

    // Creating an extension for getting user added constraints
    var userAddedConstraints: [NSLayoutConstraint] {
        return constraints.filter { c in
            guard let cId = c.identifier else { return true }
            return !cId.contains("UIView-Encapsulated-Layout")
        }
    }

    func setIsHidden(_ hidden: Bool, animated: Bool) {
        if animated {
            if self.isHidden && !hidden {
                self.alpha = 0.0
                self.isHidden = false
            }

            UIView.animate(withDuration: 0.25, animations: {
                self.alpha = hidden ? 0.0 : 1.0
            }) { (complete) in
                self.isHidden = hidden
            }
        } else {
            self.isHidden = hidden
        }
    }
}

protocol UXComponent: AnyObject {
    var isStyleApplied: Bool { get set }

    /// Called only once during the proces of the initializaton of the component
    func configure()
    /// Called right after configure() to setup all the component actions
    func defineActions()
    /// Called whenever UI needs to refresh this component data, look&feel and after defineActions()
    func handleRefresh()
}

class UXView: UIView, UXComponent {
    var defaultParent: UIViewController?

    // MARK: Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        initialize()
    }

    convenience init() {
        self.init(frame: CGRect.zero)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initialize()
    }

    private func initialize() {
        configure()
        defineActions()
        refresh()
    }

    // MARK: Visibility
    public func isVisible() -> Bool {
        if self.window == nil {
            return false
        }

        var currentView: UIView = self

        while let superview = currentView.superview {
            if (superview.bounds).intersects(currentView.frame) == false {
                return false
            }

            if currentView.isHidden {
                return false
            }

            currentView = superview
        }

        return true
    }

    var scrollViewContentInset: UIEdgeInsets?

    /// Set the top anchor constraint equal to safeAreaLayoutGuide.topAnchor.
    /// override in your class to prevent views to go under the status and navigation bar
    func setTopAnchorFollowSafeAreaLayoutGuide() {}

    // MARK: Configure

    /// Usually called only once during the proces of the initializaton of the component
    /// but it can be called multiple time to reconfigure the view.
    func configure() {
        // remove all views
        subviews.forEach({ $0.removeFromSuperview() })
    }

    /// Called right after intial call to configure() to setup all the component actions.
    func defineActions() {}

    /// Override in your class and handle refresh/update/reload the ui elements.
    /// The refresh will be called once after defineActions() and when UI needs to be refreshed.
    /// DO NOT CALL THIS FUNCTION DIRECTLY! If you need to force refresh then please call the refresh() method instead!
    func handleRefresh() {
        // TODO: Check is it safe to change the default implementation
        // and call handleRefresh() on UXView subviews.
        for case let view as UXView in subviews {
            view.handleRefresh()
        }
    }

    func clearSubviews() {
        for v in self.subviews {
            v.removeFromSuperview()
        }
    }

    /// This function is called right after refresh is finished and layoutIfNeeded is called
    /// but before any animation is triggered
    func afterRefresh() {
        for case let view as UXView in subviews {
            view.afterRefresh()
        }
    }

    private var pendingRefreshWI: DispatchWorkItem?
    /// Call when you need to refresh the UI.
    /// This function will call refresh() on the main thread and only once per execution cycle.
    final func refresh(animated: Bool = false, _ completion: ((UXView) -> Void)? = nil) {
        pendingRefreshWI?.cancel()
        pendingRefreshWI = DispatchWorkItem { [weak self] in
            guard let this = self else {
                return
            }

            let selectedTextField = this.selectedTextField
            this.handleRefresh()
            this.layoutIfNeeded()
            this.afterRefresh()

            if animated {
                UIView.transition(
                    with: this,
                    duration: 0.25,
                    options: .transitionCrossDissolve,
                    animations: {}
                ) { (done) in
                    if done {
                        selectedTextField?.becomeFirstResponder()

                        if let this = self, let completion = completion {
                            completion(this)
                        }
                    }
                }
            } else {
                selectedTextField?.becomeFirstResponder()

                if let this = self, let completion = completion {
                    completion(this)
                }
            }
        }

        if let pendingRefreshWI = pendingRefreshWI {
            DispatchQueue.main.async(execute: pendingRefreshWI)
        }
    }

    var isStyleApplied: Bool = false

    func applyDefaultStyle() {
        // check if styling is already applied
        if isStyleApplied {
            return
        }

        // apply style to children
        for case let view as UXView in subviews {
            view.applyDefaultStyle()
        }

        // apply to self
        var superv = superview

        while let sv = superv {
            if let s: RlmStyle<Self> = Self.getStyle(
                whenContainedIn: type(of: sv)
            ) {
                self.style(s)
                return
            }
            superv = sv.superview
        }

        if let s: RlmStyle<Self> = Self.getDefaultStyle() {
            self.style(s)
        }
    }

    override func didMoveToSuperview() {
        super.didMoveToSuperview()

        // apply default style
        applyDefaultStyle()
    }

    /// Will be called by UXBaseViewController from the viewWillAppear method
    /// Override in your view to get notified when view change it's visibility
    func willAppear(_ animated: Bool) {
        _onWillAppear?(self, animated)

        for v in subviews {
            if let xv = v as? UXView, !xv.isHidden,
               self.bounds.intersects(xv.frame) == true {
                xv.willAppear(animated)
            }
        }
    }

    /// Will be called by UXBaseViewController from the viewDidAppear method
    /// Override in your view to get notified when view change it's visibility
    func didAppear(_ animated: Bool) {
        _onDidAppear?(self, animated)

        for v in subviews {
            if let xv = v as? UXView, !xv.isHidden,
               self.bounds.intersects(xv.frame) == true {
                xv.didAppear(animated)
            }
        }
    }

    /// Will be called by UXBaseViewController from the viewWillDisappear method
    /// Override in your view to get notified when view change it's visibility
    func willDisappear(_ animated: Bool) {
        _onWillDisappear?(self, animated)

        for v in subviews {
            if let xv = v as? UXView, !xv.isHidden,
               self.bounds.intersects(xv.frame) == true {
                xv.willDisappear(animated)
            }
        }
    }

    /// Will be called by UXBaseViewController from the viewDidDisappear method
    /// Override in your view to get notified when view change it's visibility
    func didDisappear(_ animated: Bool) {
        _onDidDisappear?(self, animated)

        for v in subviews {
            if let xv = v as? UXView, !xv.isHidden,
               self.bounds.intersects(xv.frame) == true {
                xv.didDisappear(animated)
            }
        }
    }

    private var _title: String?

    var title: String? {
        set {
            _title = newValue?.localized()
        }
        get {
            return _title
        }
    }

    func setTitle(_ title: String?) {
        _title = title?.localized()
    }

    var userValue: Any?

    /// Override in your UXView to adjust the content insets of a view
    /// Default implementation does nothing
    func setContentInsets(_ insets: UIEdgeInsets) {
        Util.debugLog("Default setContentInset implementation called which does NOTHING!")
    }

    private var _onTap: ((UXView) -> Void)?
    private var _tapGesture: UITapGestureRecognizer?

    @discardableResult
    func onTap(_ handler: ((UXView) -> Void)?) -> UXView {
        if handler == nil, let guesture = _tapGesture {
            removeGestureRecognizer(guesture)
            _tapGesture = nil
        } else if _tapGesture == nil {
            isUserInteractionEnabled = true
            _tapGesture = UITapGestureRecognizer(
                target: self,
                action: #selector(onTapAction)
            )
            if let guesture = _tapGesture {
                addGestureRecognizer(guesture)
            }
        }
        self._onTap = handler

        return self
    }

    @objc private func onTapAction(_ sender: UITapGestureRecognizer) {
        if sender.state == .ended {
            self._onTap?(self)
        }
    }

    private var _onWillAppear: ((UXView, Bool) -> Void)?

    @discardableResult
    func onWillAppear(_ handler: @escaping ((UXView, Bool) -> Void)) -> UXView {
        self._onWillAppear = handler
        return self
    }

    private var _onDidAppear: ((UXView, Bool) -> Void)?

    @discardableResult
    func onDidAppear(_ handler: @escaping ((UXView, Bool) -> Void)) -> UXView {
        self._onDidAppear = handler
        return self
    }

    private var _onWillDisappear: ((UXView, Bool) -> Void)?

    @discardableResult
    func onWillDisappear(_ handler: @escaping ((UXView, Bool) -> Void)) -> UXView {
        self._onWillDisappear = handler
        return self
    }

    private var _onDidDisappear: ((UXView, Bool) -> Void)?

    @discardableResult
    func onDidDisappear(_ handler: @escaping ((UXView, Bool) -> Void)) -> UXView {
        self._onDidDisappear = handler
        return self
    }
}
