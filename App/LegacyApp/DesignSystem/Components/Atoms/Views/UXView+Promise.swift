//
//  UXView+Promise.swift
//  computerBOB
//
//  Created by <PERSON> on 14/12/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import CRThen

extension UXView {
    /// Update promise will call the refresh() and resolve it after it is done
    func update() -> Promise<Void> {
        return Promise { resolve, reject in
            self.refresh { _ in
                resolve()
            }
        }
    }

}
