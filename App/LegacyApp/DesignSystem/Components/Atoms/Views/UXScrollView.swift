//
//  UXScrollView.swift
//  radio.likemee
//
//  Created by <PERSON> on 8/5/20.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit

extension UIScrollView {
    /// Scroll to a specific view so that it's top is at the top our scrollview
    func scrollToView(view: <PERSON>I<PERSON>ie<PERSON>, animated: <PERSON><PERSON>) {
        if let origin = view.superview {
            // Get the Y position of your child view
            let childStartPoint = origin.convert(view.frame.origin, to: self)

            // Scroll to a rectangle starting at the Y of your subview
            // with a height of the scrollview
            self.scrollRectToVisible(
                CGRect(
                    x: 0,
                    y: childStartPoint.y,
                    width: 1,
                    height: self.frame.height
                ),
                animated: animated
            )
        }
    }

    /// Scroll to top
    func scrollToTop(animated: Bool) {
        let topOffset = CGPoint(x: 0, y: -contentInset.top)
        setContentOffset(topOffset, animated: animated)
    }

    /// Scroll to bottom
    func scrollToBottom(animated: Bo<PERSON>) {
        let bottomOffset = CGPoint(
            x: 0,
            y: contentSize.height - bounds.size.height
            + contentInset.bottom + safeAreaInsets.bottom
        )

        if bottomOffset.y > 0 {
            setContentOffset(bottomOffset, animated: animated)
        }
    }
}

class UXScrollView: UIScrollView {
    override init(frame: CGRect) {
        super.init(frame: frame)
        configure()
    }

    convenience init() {
        self.init(frame: CGRect.zero)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        configure()
    }

    func configure() {}
}
