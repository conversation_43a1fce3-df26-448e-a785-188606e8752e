//
//  UXShadowView.swift
//  
//
//  Created by <PERSON> on 21/05/2020.
//  Copyright © 2020 ComputerRock. All rights reserved.
//

import UIKit

class UXShadowView: UIView {
    weak var topView: UIView?
    var shadows: [ShadowType] = []

    func animateLayer<Value>(
        _ keyPath: WritableKeyPath<CALayer, Value>,
        to value: Value,
        duration: CFTimeInterval
    ) {
        animateLayer(self.layer, keyPath, to: value, duration: duration)
    }

    func animateLayer<Value>(
        _ layer: CALayer,
        _ keyPath: WritableKeyPath<CALayer, Value>,
        to value: Value,
        duration: CFTimeInterval
    ) {
        let keyString = NSExpression(forKeyPath: keyPath).keyPath
        let animation = CABasicAnimation(keyPath: keyString)
        animation.fromValue = self.layer[keyPath: keyPath]
        animation.toValue = value
        animation.duration = duration
        layer.add(animation, forKey: animation.keyPath)

        var thelayer = layer
        thelayer[keyPath: keyPath] = value
    }

    override func layoutSubviews() {
        super.layoutSubviews()

        remakeShadows()
    }

    private func remakeShadows() {
        if let sublayers = layer.sublayers {
            for sl in sublayers {
                sl.removeFromSuperlayer()
            }
        }

        for shadow in shadows {
            makeShadow(shadow)
        }
    }

    fileprivate func appendShadow(_ shadow: ShadowType) {
        shadows.append(shadow)
        makeShadow(shadow)
    }

    private func makeShadow(_ shadow: ShadowType) {
        switch shadow {
        case let .rect(color, alpha, offset, blur, spread):
            makeShadow(
                color: color,
                alpha: alpha,
                offset: offset,
                blur: blur,
                spread: spread
            )
        case let .circle(color, alpha, offset, blur, spread):
            makeCircleShadow(
                color: color,
                alpha: alpha,
                offset: offset,
                blur: blur,
                spread: spread
            )
        case let .distant(color, alpha, size, distance, blur):
            makeDistantShadow(
                color: color,
                alpha: alpha,
                size: size,
                distance: distance,
                blur: blur
            )
        case let .deep(color, alpha, width, height, blur):
            makeDeepShadow(
                color: color,
                alpha: alpha,
                width: width,
                height: height,
                blur: blur
            )
        }
    }

    private func makeShadow(
        color: UIColor = UIColor.black,
        alpha: Float = 0.2,
        offset: CGSize = CGSize(width: 0, height: 1),
        blur: CGFloat = 1,
        spread: CGFloat = 0
    ) {
        let newShape = CAShapeLayer()
        let cornerRadius = topView?.layer.cornerRadius ?? 0
        newShape.shadowPath = UIBezierPath(
            roundedRect: bounds.insetBy(dx: -spread, dy: -spread),
            cornerRadius: cornerRadius
        ).cgPath
        newShape.fillColor = UIColor.clear.cgColor
        layer.addSublayer(newShape)

        newShape.shadowColor = color.cgColor
        newShape.shadowOpacity = alpha
        newShape.shadowOffset = offset
        newShape.shadowRadius = blur / 2
        newShape.masksToBounds = false

        animateLayer(newShape, \.shadowOpacity, to: alpha, duration: 0.8)
     }

    private func makeCircleShadow(
        color: UIColor = UIColor.black,
        alpha: Float = 0.2,
        offset: CGSize = CGSize(width: 0, height: 1),
        blur: CGFloat = 1,
        spread: CGFloat = 0
    ) {
        let newShape = CAShapeLayer()

        let centerPoint: CGPoint = CGPoint(
            x: bounds.size.width * 0.5,
            y: bounds.size.height * 0.5
        )
        let radius = bounds.size.width * 0.5

        newShape.shadowPath = UIBezierPath(
            arcCenter: centerPoint,
            radius: radius + spread,
            startAngle: CGFloat(0),
            endAngle: CGFloat(Double.pi * 2),
            clockwise: true
        ).cgPath
        newShape.fillColor = UIColor.clear.cgColor
        layer.addSublayer(newShape)

        newShape.shadowColor = color.cgColor
        newShape.shadowOpacity = alpha
        newShape.shadowOffset = offset
        newShape.shadowRadius = blur / 2
        newShape.masksToBounds = false

        animateLayer(newShape, \.shadowOpacity, to: alpha, duration: 0.8)
    }

    private func makeDistantShadow(
        color: UIColor = UIColor.black,
        alpha: Float = 0.2,
        size: CGFloat = 10,
        distance: CGFloat = 20,
        blur: CGFloat = 10
    ) {
        let newShape = CAShapeLayer()

        let contactRect = CGRect(
            x: -size,
            y: bounds.height - (size * 0.4) + distance,
            width: bounds.width + size * 2,
            height: size
        )
        newShape.shadowPath = UIBezierPath(ovalIn: contactRect).cgPath
        layer.addSublayer(newShape)

        newShape.shadowRadius = blur / 2
        newShape.shadowOpacity = alpha
        newShape.shadowOffset = .zero
        newShape.masksToBounds = false

        animateLayer(newShape, \.shadowOpacity, to: alpha, duration: 0.8)
    }

    private func makeDeepShadow(
        color: UIColor = UIColor.black,
        alpha: Float = 0.2,
        width: CGFloat = 1.25,
        height: CGFloat = 0.5,
        blur: CGFloat = 10
    ) {
        let shadowRadius: CGFloat = blur / 2

        let newShape = CAShapeLayer()
        let shadowPath = UIBezierPath()

        shadowPath.move(
            to: CGPoint(
                x: shadowRadius / 2,
                y: bounds.height - shadowRadius / 2
            )
        )
        shadowPath.addLine(
            to: CGPoint(
                x: bounds.width - shadowRadius / 2,
                y: bounds.height - shadowRadius / 2
            )
        )
        shadowPath.addLine(
            to: CGPoint(
                x: bounds.width * width,
                y: bounds.height + (bounds.height * height)
            )
        )
        shadowPath.addLine(
            to: CGPoint(
                x: bounds.width * -(width - 1),
                y: bounds.height + (bounds.height * height)
            )
        )

        newShape.shadowPath = shadowPath.cgPath

        layer.addSublayer(newShape)

        newShape.shadowRadius = blur / 2
        newShape.shadowOpacity = alpha
        newShape.shadowOffset = .zero
        newShape.masksToBounds = false

        animateLayer(newShape, \.shadowOpacity, to: alpha, duration: 0.8)
    }
}

extension UIView {
    enum ShadowType {
        case rect(UIColor, Float, CGSize, CGFloat, CGFloat)
        case circle(UIColor, Float, CGSize, CGFloat, CGFloat)
        case distant(UIColor, Float, CGFloat, CGFloat, CGFloat)
        case deep(UIColor, Float, CGFloat, CGFloat, CGFloat)
    }

    var shadowView: UXShadowView? {
        if let shadow = superview?.subviews.filter({
            if let sw = $0 as? UXShadowView {
                return sw.topView == self
            }
            return false
        }).first as? UXShadowView {
            return shadow
        }

        if let sv = superview {
            let shadow = UXShadowView(frame: frame)
            shadow.topView = self
            shadow.bounds = bounds
            shadow.backgroundColor = UIColor.clear
            shadow.clipsToBounds = false
            shadow.layer.masksToBounds = false

            shadow.translatesAutoresizingMaskIntoConstraints = false
            // translatesAutoresizingMaskIntoConstraints = false
            sv.insertSubview(shadow, belowSubview: self)

            sv.addConstraint(
                NSLayoutConstraint(
                    item: shadow,
                    attribute: .top,
                    relatedBy: .equal,
                    toItem: self,
                    attribute: .top,
                    multiplier: 1,
                    constant: 0
                )
            )
            sv.addConstraint(
                NSLayoutConstraint(
                    item: shadow,
                    attribute: .bottom,
                    relatedBy: .equal,
                    toItem: self,
                    attribute: .bottom,
                    multiplier: 1,
                    constant: 0
                )
            )
            sv.addConstraint(
                NSLayoutConstraint(
                    item: shadow,
                    attribute: .leading,
                    relatedBy: .equal,
                    toItem: self,
                    attribute: .leading,
                    multiplier: 1,
                    constant: 0
                )
            )
            sv.addConstraint(
                NSLayoutConstraint(
                    item: shadow,
                    attribute: .trailing,
                    relatedBy: .equal,
                    toItem: self,
                    attribute: .trailing,
                    multiplier: 1,
                    constant: 0
                )
            )
            return shadow
        }
        return nil
    }

    func removeShadows() {
        if let shadowView = shadowView {
            shadowView.shadows = []
            shadowView.layer.sublayers?.forEach { $0.removeFromSuperlayer() }
            shadowView.removeFromSuperview()
        }
    }

    func addShadow(_ shadow: ShadowType) {
        shadowView?.appendShadow(shadow)
    }

    func addShadow(
        color: UIColor = UIColor.black,
        alpha: Float = 0.2,
        offset: CGSize = CGSize(width: 0, height: 1),
        blur: CGFloat = 1,
        spread: CGFloat = 0
    ) {
        shadowView?.appendShadow(.rect(color, alpha, offset, blur, spread))
    }

    func addCircleShadow(
        color: UIColor = UIColor.black,
        alpha: Float = 0.2,
        offset: CGSize = CGSize(width: 0, height: 1),
        blur: CGFloat = 1,
        spread: CGFloat = 0
    ) {
        shadowView?.appendShadow(.circle(color, alpha, offset, blur, spread))
    }

    func addDistantShadow(
        color: UIColor = UIColor.black,
        alpha: Float = 0.2,
        size: CGFloat = 10,
        distance: CGFloat = 20,
        blur: CGFloat = 10
    ) {
        shadowView?.appendShadow(.distant(color, alpha, size, distance, blur))
    }

    func addDeepShadow(
        color: UIColor = UIColor.black,
        alpha: Float = 0.2,
        width: CGFloat = 1.25,
        height: CGFloat = 0.5,
        blur: CGFloat = 10
    ) {
        shadowView?.appendShadow(.deep(color, alpha, width, height, blur))
    }
}
