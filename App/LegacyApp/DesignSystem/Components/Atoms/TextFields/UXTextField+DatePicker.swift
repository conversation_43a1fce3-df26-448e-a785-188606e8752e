//
//  UXTextField+DatePicker.swift
//  radio.likemee
//
//  Created by <PERSON> on 27/11/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit

extension UXTextField {
    struct BirthdayPicker {
        static let minYear = 200
        static let maxYear = 0
        static let defaultDate = "15.06.1980"
    }

    @discardableResult
    @objc func addDatePicker() -> Self {
        let x = getPickerAndButton()
        inputView = x.picker
        inputViewAccessory = x.accessory
        return self
    }

    fileprivate func getPickerAndButton() -> (accessory: UXButton, picker: UIDatePicker) {
        let picker = UIDatePicker()

        picker.preferredDatePickerStyle = .wheels

        // FIXME: revisit this fix for issue RLM-1765 for 80s80s (usage of undocumented "textColor")
        picker.setValue(RLMColor.datePickerText.color, forKeyPath: "textColor")

        picker.isOpaque = false
        picker.backgroundColor = RLMColor.datePickerBackgroundColor.color
        picker.datePickerMode = .date
        picker.locale = Locale.current

        let calendar = NSCalendar.current
        var components = DateComponents()
        components.year = -BirthdayPicker.maxYear
        picker.maximumDate = calendar.date(byAdding: components, to: Date())
        components.year = -BirthdayPicker.minYear
        picker.minimumDate = calendar.date(byAdding: components, to: Date())

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd.MM.yyyy"
        if let defaultPickerDate = dateFormatter.date(from: BirthdayPicker.defaultDate) {
            picker.setDate(defaultPickerDate, animated: false)
        }

        picker.addTarget(self, action: #selector(datePickerValueChanged), for: .valueChanged)

        if let text = text, text.isEmpty == false {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "dd.MM.yyyy"
            picker.date = dateFormatter.date(from: text) ?? Date()
        }

        // should we make button with registerSpinnerButtonBackground color ???
        let doneButton = UXButton(title: "button_next")
            .style(UXButton.Styles.ctaDefault)

        doneButton.onPress { [weak self] _ in
            self?.doneButtonTapped()
        }

        doneButton.translatesAutoresizingMaskIntoConstraints = false
        doneButton.height(RLMHeight.standard)

        return (accessory: doneButton, picker: picker)
    }

    @objc func datePickerValueChanged(sender: UIDatePicker) {
        // birthday = sender.date
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        dateFormatter.dateFormat = "dd.MM.yyyy"
        text = dateFormatter.string(from: sender.date)
    }
}

class UXTextFieldDatePicker: UXTextField {
    private var picker: UIDatePicker?
    private var accessory: UXButton?

    @objc override func addDatePicker() -> Self {
        guard UIDevice.current.userInterfaceIdiom == UIUserInterfaceIdiom.pad else {
            DispatchQueue.main.async {
                let x = self.getPickerAndButton()
                self.inputView = x.picker
                self.inputViewAccessory = x.accessory
            }
            return self
        }

        let x = self.getPickerAndButton()
        self.picker = x.picker
        self.accessory = x.accessory
        self.textFieldDelegate = self

        return self
    }

    override func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        guard UIDevice.current.userInterfaceIdiom == UIUserInterfaceIdiom.pad else {
            return true
        }

        guard let pick = self.picker, let acc = self.accessory else {
            return false
        }

        if let text = text, text.isEmpty == false {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "dd.MM.yyyy"
            pick.date = dateFormatter.date(from: text) ?? Date()
        }

        let vc = UIViewController()
        vc.view.translatesAutoresizingMaskIntoConstraints = false
        pick.translatesAutoresizingMaskIntoConstraints = false

        vc.view.frame = CGRect(
            x: 0,
            y: 0,
            width: 320,
            height: pick.frame.size.height
        )
        pick.frame.origin = CGPoint(
            x: 0,
            y: acc.frame.size.height
        )
        pick.frame.size.width = 320
        vc.view.addSubview(pick)

        vc.view.addConstraints(
            NSLayoutConstraint.constraints(
                withVisualFormat: "H:|-0-[v(==320)]-0-|",
                options: .directionLeadingToTrailing,
                metrics: nil,
                views: ["v": pick]
            )
        )
        vc.view.addConstraints(
            NSLayoutConstraint.constraints(
                withVisualFormat: "V:|-0-[picker(==\(pick.frame.size.height))]-0-|",
                options: .directionLeadingToTrailing,
                metrics: nil,
                views: ["accessory": acc, "picker": pick]
            )
        )
        vc.view.backgroundColor = RLMColor.datePickerBackgroundColor.color
        vc.modalPresentationStyle = .popover
        pick.backgroundColor = RLMColor.datePickerBackgroundColor.color

        let popOverVC = vc.popoverPresentationController
        popOverVC?.delegate = self.delegate as? UIPopoverPresentationControllerDelegate
        popOverVC?.sourceView = textField
        vc.preferredContentSize = CGSize(width: 320, height: pick.frame.size.height)

        UIApplication.topViewController()?.present(vc, animated: true)

        return false
    }
}
