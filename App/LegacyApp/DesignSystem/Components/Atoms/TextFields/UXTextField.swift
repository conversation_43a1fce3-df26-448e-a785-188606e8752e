//
//  UXTextField.swift
//  
//
//  Created by <PERSON> on 04/05/2020.
//  Copyright © 2019 ComputerRock. All rights reserved.
//

import UIKit
import Stevia

// --------------------------------------------------------------------------------------
/// TODO: UXTextField needs to be adjusted to fit RLM WLS needs and then styles should be created accordingly!
// --------------------------------------------------------------------------------------

@objc protocol UXTextFieldDelegate {
    @objc optional func characterLimitExceeded(inTextField: UXTextField)
    @objc optional func charactersI<PERSON>ideLimit(inTextField: UXTextField)
    @objc optional func textFieldDidBeginEditing(_ textField: UITextField)
}

protocol ValidatableField {
    @discardableResult func validate() -> Bool
    var validationErrorMessage: String? { get set }
}

private class UXTextFieldBase: UITextField {
    var validationErrorMessage: String?
    private var keyboardAvoidingHandler: KeyboardAvoidingHandler?

    override func didMoveToWindow() {
        super.didMoveToWindow()

        if keyboardAvoidingHandler == nil {
            keyboardAvoidingHandler = KeyboardAvoidingHandler(inView: self)
        }

        keyboardAvoidingHandler?.didMoveToWindow(window: window)
    }

    // RLM-2372
    // calculates y delta for centering font, need to be added to padding bottom, substracted from top
    private var yDelta: CGFloat {
        var result = 0.0
        if let typingAttributes = typingAttributes,
           let font = typingAttributes[.font] as? UIFont {
            let lineHeight = (typingAttributes[.paragraphStyle] as? NSParagraphStyle)?.minimumLineHeight ?? font.lineHeight
            let fixedOffset: CGFloat = typingAttributes[.baselineOffset] as? CGFloat ?? 0.0

            result = fixedOffset * 2.0 + (lineHeight > 0 ? (lineHeight - font.lineHeight) * 0.5 : 0.0)
        }
        return result
    }

    var padding = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)

    override open func textRect(forBounds bounds: CGRect) -> CGRect {
        return bounds.inset(by: padding.moveUp(by: self.yDelta))
    }

    var paddingPlaceholder = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
    
    override open func placeholderRect(forBounds bounds: CGRect) -> CGRect {
        return bounds.inset(by: paddingPlaceholder)
    }

    override open func editingRect(forBounds bounds: CGRect) -> CGRect {
        return bounds.inset(by: padding.moveUp(by: self.yDelta))
    }

    override open func rightViewRect(forBounds bounds: CGRect) -> CGRect {
        var rect = super.rightViewRect(forBounds: bounds)
        rect.origin = CGPoint(x: rect.origin.x - padding.right, y: rect.origin.y)

        return rect
    }

    override open func caretRect(for position: UITextPosition) -> CGRect {
        var bounds = super.caretRect(for: position)
        var cH = frame.height * 0.65

        if let at = typingAttributes, let font = at[.font] as? UIFont {
            let lH = (at[.paragraphStyle] as? NSParagraphStyle)?.minimumLineHeight ?? font.lineHeight

            if lH > cH {
                cH = lH
            }
        }
        bounds.origin.y = (frame.height - cH) / 2.0 + self.yDelta
        bounds.size.height = cH

        return bounds
    }
}

struct ValidationError: Error {
    var message: String

    init(_ message: String) {
        self.message = message.localized()
    }
}

class UXTextField: UXView, UITextFieldDelegate, ValidatableField {
    convenience init(
        placeholder: String,
        withStyle s: RlmStyle<UXTextField>? = nil,
        preferedHeight: CGFloat = RLMHeight.standard
    ) {
        self.init(frame: .zero)

        self.placeholder = placeholder

        self.preferedHeight = preferedHeight
        setHeight(preferedHeight)

        if let s = s {
            style(s)
        }
    }

    var preferedHeight: CGFloat? {
        didSet {
            if let h = preferedHeight {
                setHeight(h)
            }
        }
    }

    override func didMoveToSuperview() {
        super.didMoveToSuperview()

        if let h = preferedHeight {
            setHeight(h)
        }
    }

    override var isFirstResponder: Bool {
        return textField.isFirstResponder
    }

    override var inputView: UIView? {
        set {
            textField.inputView = newValue
        }
        get {
            return textField.inputView
        }
    }

    var inputViewAccessory: UIView? {
        set {
            textField.inputAccessoryView = newValue
        }
        get {
            return textField.inputAccessoryView
        }
    }

    @discardableResult
    override func becomeFirstResponder() -> Bool {
        return textField.becomeFirstResponder()
    }

    @discardableResult
    override func resignFirstResponder() -> Bool {
        return textField.resignFirstResponder()
    }

    var isEnabled: Bool {
        return textField.isEnabled
    }

    @discardableResult
    func setIsEnabled(_ enabled: Bool) -> Self {
        textField.isEnabled = enabled
        editingStatusChange()
        return self
    }

    public enum State {
        case normal
        case selected
        case disabled
        case error
    }

    struct StateAttributes {
        var backgroundColor: UIColor?
        var showBorder: Bool?
        var borderColor: UIColor?
        var borderWidth: CGFloat?
        var borderCornerRadius: CGFloat?
        var showBottomLine: Bool?
        var bottomLineColor: UIColor?
        var bottomLineHeight: CGFloat?
        var textStyle: UITextStyle?
        var textColor: UIColor?
        var placeholderStyle: UITextStyle?
        var placeholderColor: UIColor?
        var floatingStyle: UITextStyle?
        var assitiveText: String?
        var assitiveTextStyle: UITextStyle?
        var opacity: CGFloat?

        public init() {}

        public init(completion: @escaping (inout StateAttributes) -> Void) {
            completion(&self)
        }
    }

    private var stateAttributes: [State: StateAttributes] = [:]

    private var currentState: State = .normal {
        didSet {
            refresh()
        }
    }

    private var currentAttributes: StateAttributes {
        if let att = stateAttributes[currentState] {
            return att
        }
        return stateAttributes[.normal] ?? StateAttributes()
    }

    private var textField = UXTextFieldBase()
    private var floatingLabel = UXLabel()
    private var assistiveTextLabel = UXLabel()
    private var bottomLineView = UIView()

    public var textFieldDelegate: UITextFieldDelegate? {
        didSet {
            textField.delegate = self.textFieldDelegate
        }
    }

    var textContentType: UITextContentType? {
        didSet {
            if let value = textContentType {
                textField.textContentType = value
            }
        }
    }

    var autocorrectionType: UITextAutocorrectionType? {
        didSet {
            if let value = autocorrectionType {
                textField.autocorrectionType = value
            }
        }
    }

    var autocapitalizationType: UITextAutocapitalizationType? {
        didSet {
            if let value = autocapitalizationType {
                textField.autocapitalizationType = value
            }
        }
    }

    var floatingLabelInsets: UIEdgeInsets? {
        didSet {
            floatingLabel.insets = floatingLabelInsets
            floatingLabel.leftConstraint?.constant = textField.padding.left - (floatingLabelInsets?.left ?? 0)
        }
    }

    var contentInsets: UIEdgeInsets? = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16) {
        didSet {
            if let contentInsets = contentInsets {
                // ignore top and bottom padding, something is not calculated correctly when they are != 0
                textField.padding = UIEdgeInsets(
                    top: 0,
                    left: contentInsets.left,
                    bottom: 0,
                    right: contentInsets.right
                )
            } else {
                textField.padding = UIEdgeInsets.zero
            }
        }
    }

    var placeholderInsets: UIEdgeInsets? = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16) {
        didSet {
            textField.paddingPlaceholder = placeholderInsets ?? UIEdgeInsets.zero
        }
    }

    private var validator: ((_ textField: UXTextField, _ value: String?) -> ValidationError?)?

    var validationErrorMessage: String?

    /// DO NOT CALL FROM VALIDATOR IT SELF
    var isTextValid: Bool {
        guard let errorMessage = validator?(self, textField.text)?.message else {
            return true
        }
        validationErrorMessage = errorMessage
        setAssistiveText(errorMessage, for: .error)
        return false
    }

    @discardableResult
    func validate(
        with validator: @escaping (_ textField: UXTextField, _ value: String?) -> ValidationError?
    ) -> UXTextField {
        self.validator = validator
        return self
    }

    /// Validate the input and changes the state of the control
    @discardableResult
    func validate() -> Bool {
        guard let errorMessage = validator?(self, textField.text)?.message else {
            return true
        }

        validationErrorMessage = errorMessage
        setAssistiveText(errorMessage, for: .error)
        editingStatusChange()

        return false
    }

    @discardableResult
    func setAsteriskHidden(_ hidden: Bool) -> Self {
        self.showAsterisk = !hidden
        return self
    }

    var showAsterisk: Bool = true {
        didSet {
            mandatory(isMandatory)
        }
    }

    var isMandatory: Bool = false {
        didSet {
            if var text = placeholder?.localized() {
                if !isMandatory, text.last == "*" {
                    text.removeLast()
                    placeholder = text
                } else if isMandatory, showAsterisk, text.last != "*" {
                    placeholder = text + "*"
                } else {
                    placeholder = text
                }
            }
        }
    }

    @discardableResult
    func mandatory(
        _ isMandatory: Bool,
        _ callback: ((UXTextField) -> Void)? = nil
    ) -> UXTextField {
        self.isMandatory = isMandatory
        callback?(self)
        return self
    }

    var allowedCharcterSet: CharacterSet? {
        didSet {
            if textField.delegate == nil {
                textField.delegate = self
            }
        }
    }

    var keyboardType: UIKeyboardType {
        set {
            textField.keyboardType = newValue

            if textField.delegate == nil {
                textField.delegate = self
            }

            if keyboardType == .numberPad || keyboardType == .phonePad {
                addDoneNextToolbar()
            } else if keyboardType == .emailAddress {
                textField.autocorrectionType = .no
                textField.autocapitalizationType = UITextAutocapitalizationType.none
            }
        }
        get {
            return textField.keyboardType
        }
    }

    @discardableResult
    func setKeyboardType(_ kt: UIKeyboardType) -> Self {
        keyboardType = kt
        return self
    }

    var returnKeyType: UIReturnKeyType {
        set {
            textField.returnKeyType = newValue
            if textField.delegate == nil {
                textField.delegate = self
            }
        }
        get {
            return textField.returnKeyType
        }
    }

    @discardableResult
    func setReturnKeyType(_ rkt: UIReturnKeyType) -> Self {
        returnKeyType = rkt
        return self
    }

    // MARK: Images
    var leftImage: UIImage? {
        didSet {
            if let img = leftImage {
                let imageView = UIImageView()
                imageView.image = img
                imageView.contentMode = .scaleAspectFit
                textField.leftView = imageView
                textField.leftViewMode = .always
            } else {
                textField.leftView = nil
                textField.leftViewMode = .never
            }
        }
    }

    var rightImage: UIImage? {
        didSet {
            if let img = rightImage {
                let imageView = UIImageView()
                imageView.image = img
                imageView.contentMode = .scaleAspectFit
                textField.rightView = imageView
                textField.rightViewMode = .always
            } else {
                textField.rightView = nil
                textField.rightViewMode = .never
            }
        }
    }

    var rightButtonViewMode: UITextField.ViewMode = .whileEditing {
        didSet {
            if let rb = rightButton {
                textField.rightView = rb
                textField.rightViewMode = rightButtonViewMode
            } else {
                textField.rightView = nil
                textField.rightViewMode = rightButtonViewMode
            }
        }
    }

    var rightButton: UXButton? {
        didSet {
            if let rb = rightButton {
                textField.rightView = rb
                textField.rightViewMode = rightButtonViewMode
            } else {
                textField.rightView = nil
                textField.rightViewMode = rightButtonViewMode
            }
        }
    }

    @discardableResult
    func setRightButton(
        _ rb: UXButton,
        _ viewMode: UITextField.ViewMode = .whileEditing,
        _ action: ((UXTextField) -> Void)?
    ) -> UXTextField {
        rb.onPress { [weak self] _ in
            guard let this = self else { return }
            action?(this)
        }
        rightButton = rb
        rightButtonViewMode = viewMode

        return self
    }

    @discardableResult
    func setStateAttributes(
        for state: State = .normal,
        _ attributes: StateAttributes
    ) -> Self {
        stateAttributes[state] = attributes

        if state == .normal, let at = attributes.textStyle {
            textField.defaultTextAttributes = at.extend {
                if let color = attributes.textColor {
                    $0[.foregroundColor] = color
                }
            }.attributes()
        }

        if currentState == state {
            refresh()
        }

        return self
    }

    @discardableResult
    func stateAttributes(
        for state: State = .normal,
        _ completion: (inout StateAttributes) -> Void
    ) -> Self {
        if var att = stateAttributes[state] {
            completion(&att)
            return setStateAttributes(for: state, att)
        } else {
            var att = StateAttributes()
            completion(&att)
            return setStateAttributes(for: state, att)
        }
    }

    // MARK: Text and placeholder
    // 0 is unlimited
    var maxNoOfChars: Int = 0

    @discardableResult
    func setMaxNoOfChars(_ mnoc: Int) -> Self {
        maxNoOfChars = mnoc
        return self
    }

    weak var delegate: UXTextFieldDelegate?

    var isSecureTextEntry: Bool = false {
        didSet {
            if isSecureTextEntry {
                textField.autocorrectionType = .no
                textField.autocapitalizationType = UITextAutocapitalizationType.none
            }
            textField.isSecureTextEntry = isSecureTextEntry
        }
    }

    var placeholder: String? {
        set {
            if let at = textField.attributedPlaceholder {
                textField.attributedPlaceholder = at.attributedString(withText: newValue ?? " ")
            } else {
                textField.placeholder = newValue
            }
            floatingLabel.setText(newValue ?? " ")
        }
        get {
            return textField.placeholder
        }
    }

    /// NOTE: Same as text property.
    override var title: String? {
        set {
            text = newValue
        }
        get {
            return text
        }
    }

    var text: String? {
        set {
            if let at = textField.attributedText {
                textField.attributedText = at.attributedString(withText: newValue ?? " ")
            } else {
                textField.text = newValue
            }
            textDidChange()
        }
        get {
            return textField.text
        }
    }

    fileprivate var initialText: String?

    /// Returns value of text property only if it is changed
    var newText: String? {
        return isChanged ? text : nil
    }

    var isChanged: Bool {
        return text != initialText
    }

    var showFloatingText: Bool = false {
        didSet {
            configure()
        }
    }

    var showAssistiveText: Bool = false {
        didSet {
            configure()
        }
    }

    private var assitiveText: String? {
        set {
            if let at = assistiveTextLabel.attributedText {
                assistiveTextLabel.attributedText = at.attributedString(withText: newValue ?? " ")
            } else {
                assistiveTextLabel.text = newValue
            }
        }
        get {
            if let at = assistiveTextLabel.attributedText {
                return at.string
            } else {
                return assistiveTextLabel.text
            }
        }
    }

    func setAssistiveText(_ text: String, for state: State) {
        stateAttributes(for: state) {
            $0.assitiveText = text
        }
    }

    override func configure() {
        subviews(
            textField,
            bottomLineView,
            floatingLabel,
            assistiveTextLabel
        )

        let topMargin: CGFloat = showFloatingText ? 9 : 0
        let showBottomLine = currentAttributes.showBottomLine ?? stateAttributes[.normal]?.showBottomLine ?? false
        let bottomLineHeight = showBottomLine ? currentAttributes.bottomLineHeight ?? stateAttributes[.normal]?.bottomLineHeight ?? 1 : 0
        var textFieldHeight = preferedHeight ?? CGFloat(RLMHeight.standard)
        textFieldHeight -= topMargin
        textFieldHeight -= bottomLineHeight

        if showAssistiveText {
            let assistiveTextLabelHeight: CGFloat = 16
            textFieldHeight -= assistiveTextLabelHeight

            layout {
                topMargin
                |-0-textField-0-| ~ textFieldHeight
                |-0-bottomLineView-0-| ~ bottomLineHeight
                3
                |-0-assistiveTextLabel-0-| ~ assistiveTextLabelHeight
                0
            }
        } else {
            layout {
                topMargin
                |-0-textField-0-| ~ textFieldHeight
                |-0-bottomLineView-0-| ~ bottomLineHeight
                0
            }
        }

        if showFloatingText {
            floatingLabel
                .top(0)
                .left(textField.padding.left)
                .right(>=textField.padding.right)
                .height(16)
        }

        textField.contentVerticalAlignment = .center
        textField.adjustsFontForContentSizeCategory = true
    }

    override func defineActions() {
        textField.addTarget(
            self,
            action: #selector(textDidChange),
            for: .editingChanged
        )
        textField.addTarget(
            self,
            action: #selector(editingStatusChange),
            for: .editingDidBegin
        )
        textField.addTarget(
            self,
            action: #selector(editingStatusChange),
            for: .editingDidEnd
        )
    }

    override func handleRefresh() {
        super.handleRefresh()

        if let c = currentAttributes.backgroundColor ?? stateAttributes[.normal]?.backgroundColor {
            textField.backgroundColor = c
        }

        if (currentAttributes.showBottomLine ?? stateAttributes[.normal]?.showBottomLine) == true,
           let c = currentAttributes.bottomLineColor ?? stateAttributes[.normal]?.bottomLineColor {
            bottomLineView.isHidden = false
            bottomLineView.backgroundColor = c
            bottomLineView.setHeight(
                currentAttributes.bottomLineHeight ?? stateAttributes[.normal]?.bottomLineHeight ?? 1.0
            )
        } else {
            bottomLineView.isHidden = true
        }

        if (currentAttributes.showBorder ?? stateAttributes[.normal]?.showBorder ?? true) == true,
           let c = currentAttributes.borderColor ?? stateAttributes[.normal]?.borderColor {
            textField.layer.borderColor = (c).cgColor
            textField.layer.borderWidth = currentAttributes.borderWidth ?? stateAttributes[.normal]?.borderWidth ?? 1.0
        } else {
            textField.layer.borderWidth = 0.0
        }

        textField.layer.cornerRadius = currentAttributes.borderCornerRadius ?? stateAttributes[.normal]?.borderCornerRadius ?? 0.0

        if let s = currentAttributes.textStyle ?? stateAttributes[.normal]?.textStyle {
            let textStyle: UITextStyle = s.extend { [weak self] in
                if let color = self?.currentAttributes.textColor ?? self?.stateAttributes[.normal]?.textColor {
                    $0[.foregroundColor] = color
                }
                /*
                 // RLM-2372
                 if let font = $0[.font] as? UIFont {
                 // Centering Font Vertically
                 let lh = ($0[.paragraphStyle] as? NSParagraphStyle)?.minimumLineHeight ?? font.lineHeight
                 let fixedOffset : CGFloat = $0[.baselineOffset] as? CGFloat ?? 0.0
                 $0[.baselineOffset] =  fixedOffset * 2.0 + ( lh > 0 ? (lh - font.lineHeight) * 0.5 : 0.0)
                 }
                 */
            }
            let textToStyle = textField.attributedText?.string ?? textField.text ?? ""
            textField.defaultTextAttributes = textStyle.attributes()
            if !textToStyle.isEmpty, textField.isEditing {
                textField.typingAttributes = textStyle.attributes()
            }
        }

        if let s = currentAttributes.placeholderStyle ?? stateAttributes[.normal]?.placeholderStyle ?? stateAttributes[.normal]?.textStyle {
            textField.attributedPlaceholder = (
                textField.attributedPlaceholder?.string ?? textField.placeholder ?? " "
            )
            .style(s.extend { [weak self] in
                if let color = self?.currentAttributes.placeholderColor ?? self?.stateAttributes[.normal]?.placeholderColor {
                    $0[.foregroundColor] = color
                }
            })
        }

        if showAssistiveText,
           let s = currentAttributes.assitiveTextStyle ?? stateAttributes[.normal]?.assitiveTextStyle {
            assistiveTextLabel.attributedText = (
                assistiveTextLabel.attributedText?.string ?? assistiveTextLabel.text ?? " "
            )
            .style(s)

            assistiveTextLabel.isHidden = false
        } else {
            assistiveTextLabel.isHidden = true
        }

        if showFloatingText,
           let s = currentAttributes.floatingStyle ?? stateAttributes[.normal]?.floatingStyle {
            if let c = currentAttributes.backgroundColor ?? stateAttributes[.normal]?.backgroundColor {
                floatingLabel.backgroundColor = c
            }
            floatingLabel.attributedText = placeholder?.style(s)

            if let count = text?.count, count > 0 && currentState == .selected {
                floatingLabel.isHidden = false
            } else {
                floatingLabel.isHidden = true
            }
        } else {
            floatingLabel.isHidden = true
        }

        if let opacity = currentAttributes.opacity {
            alpha = opacity
        } else {
            alpha = 1.0
        }

        if rightButtonViewMode != .always && (text == nil || text?.count == 0 || !textField.isEnabled || currentState == .error) {
            textField.rightView = nil
        } else {
            textField.rightView = rightButton
        }
    }

    @objc private func editingStatusChange() {
        if !textField.isEnabled {
            currentState = .disabled
            refresh()
        } else if textField.isEditing {
            currentState = .selected
            refresh()
            textDidChange()
            delegate?.textFieldDidBeginEditing?(textField)
        } else if isTextValid == false {
            currentState = .error
            refresh()
            self._onValidationFalied?(self)
        } else {
            currentState = .normal
            refresh()
            self._onChanged?(self)
        }
        assitiveText = currentAttributes.assitiveText ?? stateAttributes[.normal]?.assitiveText ?? " "
    }

    private var _onTextDidChange: ((UXTextField) -> Void)?

    /// Called whenever uset types something in the text field
    @discardableResult
    func onTextDidChange(_ callback: ((UXTextField) -> Void)?) -> UXTextField {
        _onTextDidChange = callback
        return self
    }

    @objc private func textDidChange() {
        defer {
            _onTextDidChange?(self)
        }

        guard let count = text?.count else {
            return
        }

        if maxNoOfChars > 0 && count > maxNoOfChars {
            text?.removeLast()
        }

        if showFloatingText {
            if count > 0 && currentState == .selected {
                floatingLabel.isHidden = false
            } else {
                floatingLabel.isHidden = true
            }
        } else {
            floatingLabel.isHidden = true
        }

        if maxNoOfChars != 0 && maxNoOfChars <= count {
            delegate?.characterLimitExceeded?(inTextField: self)
        } else {
            delegate?.charactersInsideLimit?(inTextField: self)
        }

        // FIX: prevent right button to show when we clear the text
        if rightButtonViewMode != .always && (text == nil || text?.count == 0 || !textField.isEnabled || currentState == .error) {
            textField.rightView = nil
        }
    }

    func textField(
        _ textField: UITextField,
        shouldChangeCharactersIn range: NSRange,
        replacementString string: String
    ) -> Bool {
        // Handle backspace/delete
        guard !string.isEmpty else {
            // Backspace detected, allow text change, no need to process the text any further
            return true
        }

        let characterSet: CharacterSet = CharacterSet(charactersIn: string)

        // Input Validation
        if let allowedCharcterSet = allowedCharcterSet {
            if allowedCharcterSet.isSuperset(of: characterSet) {
                // Invalid characters detected, disallow text change
                return false
            }
        } else if textField.keyboardType == .numberPad {
            // Prevent invalid character input, if keyboard is numberpad

            // Check for invalid input characters
            if !CharacterSet(charactersIn: "0123456789")
                .isSuperset(of: characterSet) {
                // Invalid characters detected, disallow text change
                return false
            }
        } else if textField.keyboardType == .alphabet {
            // Check for invalid input characters
            if !(CharacterSet.letters.isSuperset(of: characterSet) ||
                 CharacterSet.whitespaces.isSuperset(of: characterSet)) {
                // Invalid characters detected, disallow text change
                return false
            }
        }

        // Length Processing
        // Need to convert the NSRange to a Swift-appropriate type
        if let text = textField.text,
           let range = Range(range, in: text),
           maxNoOfChars > 0 {
            let proposedText = text.replacingCharacters(in: range, with: string)

            // Check proposed text length does not exceed max character count
            guard proposedText.count <= maxNoOfChars else {
                // Character count exceeded, disallow text change
                return false
            }
        }

        // Allow text change
        return true
    }

    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if let orf = _onReturn {
            orf(self)
            return false
        }

        return true
    }

    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        return true
    }

    private var _onChanged: ((UXTextField) -> Void)?

    /// Called when user ends editing and text field value passes the validation
    @discardableResult
    func onChanged(_ handler: @escaping ((UXTextField) -> Void)) -> UXTextField {
        self._onChanged = handler
        return self
    }

    private var _onValidationFalied: ((UXTextField) -> Void)?

    @discardableResult
    func onValidationFalied(_ handler: @escaping ((UXTextField) -> Void)) -> UXTextField {
        self._onValidationFalied = handler
        return self
    }

    private var _onReturn: ((UXTextField) -> Void)?

    @discardableResult
    func onReturn(_ handler: @escaping ((UXTextField) -> Void)) -> UXTextField {
        self._onReturn = handler
        if textField.delegate == nil {
            textField.delegate = self
        }

        return self
    }

    /// DONE NEXT TOOLBAR
    private var _onDone: ((UXTextField) -> Void)?
    private var _onNext: ((UXTextField) -> Void)?

    @discardableResult
    func addDoneNextToolbar(
        doneTitle: String = "keyboard_toolbar_done",
        nextTitle: String = "keyboard_toolbar_next"
    ) -> UXTextField {
        guard textField.inputAccessoryView == nil else {
            return self
        }

        let toolbar: UIToolbar = UIToolbar()
        toolbar.barStyle = .default
        toolbar.items = [
            UIBarButtonItem(
                title: doneTitle.localized(),
                style: .done,
                target: self,
                action: #selector(doneButtonTapped)
            ),
            UIBarButtonItem(
                barButtonSystemItem: .flexibleSpace,
                target: self,
                action: nil
            ),
            UIBarButtonItem(
                title: nextTitle.localized(),
                style: .plain,
                target: self,
                action: #selector(nextButtonTapped)
            )
        ]

        toolbar.sizeToFit()
        textField.inputAccessoryView = toolbar

        return self
    }

    @objc func doneButtonTapped() {
        if let action = _onDone {
            action(self)
            return
        }
        self.resignFirstResponder()
    }

    @objc func nextButtonTapped() {
        if let action = _onNext {
            action(self)
            return
        }
        if let action = _onReturn {
            action(self)
            return
        }
        self.resignFirstResponder()

        if let next = self.next {
            next.becomeFirstResponder()
        }
    }

    @discardableResult
    func onDone(_ handler: @escaping ((UXTextField) -> Void)) -> UXTextField {
        self._onDone = handler
        return self
    }

    @discardableResult
    func onNext(_ handler: @escaping ((UXTextField) -> Void)) -> UXTextField {
        self._onNext = handler
        return self
    }
}

extension UXTextField {
    /// Set the text and localize it. This will set the initial text value as well.
    func setText(_ txt: String?, _ uppercased: Bool = false ) {
        guard let txt = txt else {
            return
        }

        self.initialText = uppercased ? txt.localized().uppercased() : txt.localized()
        self.text = self.initialText
    }
}
