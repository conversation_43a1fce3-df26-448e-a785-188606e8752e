//
//  UXTextView.swift
//  radio.likemee
//
//  Created by <PERSON> on 25/11/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXTextView: UXView, UITextViewDelegate, ValidatableField {
    typealias State = UXTextField.State
    typealias StateAttributes = UXTextField.StateAttributes

    // 0 is unlimited
    var maxNoOfChars: Int = 0
    var resizableWhileTyping: Bool = false

    convenience init(
        placeholder: String,
        withStyle s: RlmStyle<UXTextView>? = nil,
        preferedHeight: CGFloat = RLMHeight.standard,
        resizableWhileTyping: Bool = false
    ) {
        self.init(frame: .zero)

        lbPlaceholder.setText(placeholder)

        self.preferedHeight = preferedHeight
        self.resizableWhileTyping = resizableWhileTyping

        if !resizableWhileTyping {
            textView.setHeight(preferedHeight)
        }

        if let s = s {
            style(s)
        }
    }

    @discardableResult
    func setMaxNoOfChars(_ mnoc: Int) -> Self {
        maxNoOfChars = mnoc
        return self
    }

    var preferedHeight: CGFloat? {
        didSet {
            if let h = preferedHeight, !resizableWhileTyping {
                textView.setHeight(h)
            }
        }
    }

    override func didMoveToWindow() {
        super.didMoveToWindow()

        if keyboardAvoidingHandler == nil {
            keyboardAvoidingHandler = KeyboardAvoidingHandler(inView: self)
        }

        keyboardAvoidingHandler?.didMoveToWindow(window: window)
    }

   override func didMoveToSuperview() {
        super.didMoveToSuperview()

        if let h = preferedHeight, !resizableWhileTyping {
            textView.setHeight(h)
        }
    }

    override var isFirstResponder: Bool {
        return textView.isFirstResponder
    }

    override var inputView: UIView? {
        set {
            textView.inputView = newValue
        }
        get {
            return textView.inputView
        }
    }

    var inputViewAccessory: UIView? {
        set {
            textView.inputAccessoryView = newValue
        }
        get {
            return textView.inputAccessoryView
        }
    }

    @discardableResult
    override func becomeFirstResponder() -> Bool {
        return textView.becomeFirstResponder()
    }

    @discardableResult
    override func resignFirstResponder() -> Bool {
        return textView.resignFirstResponder()
    }

    var isScrollEnabled: Bool {
        set {
            textView.isScrollEnabled = newValue
        }
        get {
            return textView.isScrollEnabled
        }
    }

    var isSelectable: Bool {
        set {
            textView.isSelectable = newValue
        }
        get {
            return textView.isSelectable
        }
    }

    var isEditable: Bool {
        set {
            textView.isEditable = newValue
        }
        get {
            return textView.isEditable
        }
    }

    /// same as isEditable
    var isEnabled: Bool {
        return textView.isEditable
    }

    /// call this method to disable the control and change the internal state to .disabled
    @discardableResult
    func setIsEnabled(_ enabled: Bool) -> Self {
        textView.isEditable = enabled
        currentState = enabled ? .normal : .disabled
        refresh()
        return self
    }

    var placeholder: String? {
        set {
            lbPlaceholder.setText(newValue ?? " ")
        }
        get {
            return lbPlaceholder.text
        }
    }

    var showPlaceholder: Bool = true {
        didSet {
            refresh()
        }
    }

    var contentInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 4) {
        didSet {
            textView.textContainerInset = contentInsets
            lbPlaceholder.topConstraint?.constant = contentInsets.top
            lbPlaceholder.insets = UIEdgeInsets(
                top: 0,
                left: contentInsets.left,
                bottom: 0,
                right: contentInsets.right
            )
            layoutIfNeeded()
        }
    }

    private var keyboardAvoidingHandler: KeyboardAvoidingHandler?

    private var stateAttributes: [State: StateAttributes] = [:]

    private var currentState: State = .normal {
        didSet {
            refresh()
        }
    }

    private var currentAttributes: StateAttributes {
        if let att = stateAttributes[currentState] {
            return att
        }
        return stateAttributes[.normal] ?? StateAttributes()
    }

    private var textView = UITextView()
    private var lbPlaceholder = UXLabel()
    private var bottomLineView = UIView()

    override func configure() {
        textView.isScrollEnabled = false
        textView.isEditable = false
        textView.textContainer.lineFragmentPadding = 0
        textView.adjustsFontForContentSizeCategory = true

        if RLMSettings.keyboardAppearanceDark {
            textView.keyboardAppearance = .dark
        } else {
            textView.keyboardAppearance = .light
        }

        subviews(
            textView,
            lbPlaceholder,
            bottomLineView
        )

        let showBottomLine = currentAttributes.showBottomLine ?? stateAttributes[.normal]?.showBottomLine ?? false
        let bottomLineHeight = showBottomLine ? currentAttributes.bottomLineHeight ?? stateAttributes[.normal]?.bottomLineHeight ?? 1 : 0

        layout {
            0
            |-0-textView-0-|
            |-0-bottomLineView-0-| ~ bottomLineHeight
            0
        }

        lbPlaceholder.Left == textView.Left
        lbPlaceholder.Right == textView.Right
        lbPlaceholder.top(contentInsets.top)
        lbPlaceholder.insets = UIEdgeInsets(
            top: 0,
            left: contentInsets.left,
            bottom: 0,
            right: contentInsets.right
        )
    }

    var text: String {
        set {
            textView.text = newValue
            lbPlaceholder.isHidden = newValue.count != 0
            refresh()
        }
        get {
            return textView.text
        }
    }

    var attributedText: NSAttributedString {
        set {
            textView.attributedText = newValue
            lbPlaceholder.isHidden = text.count != 0
            refresh()
        }
        get {
            return textView.attributedText
        }
    }

    var linkTextAttributes: [NSAttributedString.Key: Any] {
        set {
            textView.linkTextAttributes = newValue
        }
        get {
            return textView.linkTextAttributes
        }
    }

    var dataDetectorTypes: UIDataDetectorTypes {
        set {
            textView.dataDetectorTypes = newValue
        }
        get {
            return textView.dataDetectorTypes
        }
    }

    @objc private func textDidChange(notification: Notification) {
        if (notification.object as? UITextView) == textView {
            lbPlaceholder.isHidden = text.count != 0

            if maxNoOfChars > 0 && text.count > maxNoOfChars {
                text.removeLast()
            }
            _onTextDidChange?(self)
        }
    }

    @objc private func textDidBeginEditing(notification: Notification) {
        currentState = .selected
        refresh()
        lbPlaceholder.isHidden = text.count != 0
        _onBeginEditing?(self)
    }

    @objc private func textDidEndEditing(notification: Notification) {
        currentState = .normal
        refresh()
        _onChanged?(self)
    }

    private var _onTextDidChange: ((UXTextView) -> Void)?

    /// Called whenever uset types something in the text field
    @discardableResult
    func onTextDidChange(_ callback: ((UXTextView) -> Void)?) -> UXTextView {
        _onTextDidChange = callback
        return self
    }

    private var _onBeginEditing: ((UXTextView) -> Void)?

    /// Called whenever uset types something in the text field
    @discardableResult
    func onBeginEditing(_ callback: ((UXTextView) -> Void)?) -> UXTextView {
        _onBeginEditing = callback
        return self
    }

    private var _onChanged: ((UXTextView) -> Void)?

    /// Called when user ends editing and text field value passes the validation
    @discardableResult
    func onChanged(_ handler: @escaping ((UXTextView) -> Void)) -> UXTextView {
        self._onChanged = handler
        return self
    }

    @discardableResult
    func setStateAttributes(for state: State = .normal, _ attributes: StateAttributes) -> Self {
        stateAttributes[state] = attributes

        if state == .normal, let at = attributes.textStyle {
            textView.typingAttributes = at.extend {
                if let color = attributes.textColor {
                    $0[.foregroundColor] = color
                }
            }.attributes()
        }

        if currentState == state {
            refresh()
        }
        return self
    }

    @discardableResult
    func stateAttributes(for state: State = .normal, _ completion: (inout StateAttributes) -> Void) -> Self {
        if var att = stateAttributes[state] {
            completion(&att)
            return setStateAttributes(for: state, att)
        } else {
            var att = StateAttributes()
            completion(&att)
            return setStateAttributes(for: state, att)
        }
    }

    override func defineActions() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(textDidChange(notification:)),
            name: UITextView.textDidChangeNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(textDidBeginEditing(notification:)),
            name: UITextView.textDidBeginEditingNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(textDidEndEditing(notification:)),
            name: UITextView.textDidEndEditingNotification,
            object: nil
        )
    }

    override func handleRefresh() {
        super.handleRefresh()

        if let c = currentAttributes.backgroundColor ?? stateAttributes[.normal]?.backgroundColor {
            textView.backgroundColor = c
        }

        if (currentAttributes.showBottomLine ?? stateAttributes[.normal]?.showBottomLine) == true,
           let c = currentAttributes.bottomLineColor ?? stateAttributes[.normal]?.bottomLineColor {
            bottomLineView.isHidden = false
            bottomLineView.backgroundColor = c
            bottomLineView.setHeight(
                currentAttributes.bottomLineHeight ?? stateAttributes[.normal]?.bottomLineHeight ?? 1.0
            )
        } else {
            bottomLineView.isHidden = true
        }

        if (currentAttributes.showBorder ?? stateAttributes[.normal]?.showBorder ?? true) == true,
           let c = currentAttributes.borderColor ?? stateAttributes[.normal]?.borderColor {
            textView.layer.borderColor = (c).cgColor
            textView.layer.borderWidth = currentAttributes.borderWidth ?? stateAttributes[.normal]?.borderWidth ?? 1.0
        } else {
            textView.layer.borderWidth = 0.0
        }
        textView.layer.cornerRadius = currentAttributes.borderCornerRadius ?? stateAttributes[.normal]?.borderCornerRadius ?? 0.0

        if let s = currentAttributes.textStyle ?? stateAttributes[.normal]?.textStyle {
            let textStyle: UITextStyle = s.extend { [weak self] in
                if let color = self?.currentAttributes.textColor ?? self?.stateAttributes[.normal]?.textColor {
                    $0[.foregroundColor] = color
                }
            }
            textView.typingAttributes = textStyle.attributes()

            let textToStyle = textView.attributedText?.string ?? textView.text ?? ""
            if !textToStyle.isEmpty {
                textView.attributedText = textToStyle.style(textStyle)
            }
        }

        if let s = currentAttributes.placeholderStyle ?? stateAttributes[.normal]?.placeholderStyle ?? stateAttributes[.normal]?.textStyle {
            lbPlaceholder.attributedText = (placeholder ?? " ").style(s.extend { [weak self] in
                if let color = self?.currentAttributes.placeholderColor ?? self?.stateAttributes[.normal]?.placeholderColor {
                    $0[.foregroundColor] = color
                }
            })
        }

        if let opacity = currentAttributes.opacity {
            alpha = opacity
        } else {
            alpha = 1.0
        }
    }

    private var validator: ((_ textField: UXTextView, _ value: String?) -> ValidationError?)? = nil

    var validationErrorMessage : String?

    /// DO NOT CALL FROM VALIDATOR IT SELF
    var isTextValid: Bool {
        guard let error_message = validator?(self, text)?.message else {
            return true
        }

        validationErrorMessage = error_message
        return false
    }

    @discardableResult
    func validate(
        with validator: @escaping (
            _ textField: UXTextView,
            _ value: String?
        ) -> ValidationError?
    ) -> UXTextView {
        self.validator = validator
        return self
    }

    /// Validate the input and changes the state of the control
    @discardableResult
    func validate() -> Bool {
        guard let error_message = validator?(self, text)?.message else {
            return true
        }

        validationErrorMessage = error_message
        currentState = .error
        refresh()
        return false
    }

    @discardableResult
    func setAsteriskHidden(_ hidden: Bool) -> Self {
        self.showAsterisk = !hidden
        return self
    }

    var showAsterisk: Bool = true {
        didSet {
            mandatory(isMandatory)
        }
    }

    var isMandatory: Bool = false {
        didSet {
            if var text = placeholder?.localized() {
                if !isMandatory, text.last == "*" {
                    text.removeLast()
                    placeholder = text
                } else if isMandatory, showAsterisk, text.last != "*" {
                    placeholder = text + "*"
                } else {
                    placeholder = text
                }
            }
        }
    }

    @discardableResult
    func mandatory(
        _ isMandatory: Bool,
        _ callback: ((UXTextView) -> Void)? = nil
    ) -> UXTextView {
        self.isMandatory = isMandatory
        callback?(self)
        return self
    }

    // TODO: Implemnt right button/image similar to uxtextfield
    var rightImageName: String? {
        didSet {}
    }

    var rightButtonViewMode: UITextField.ViewMode = .whileEditing {
        didSet {}
    }

    var rightButton: UXButton? {
        didSet {}
    }
}
