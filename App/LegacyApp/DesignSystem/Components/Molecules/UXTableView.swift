//
//  UXTableView.swift
//  radio.likemee
//
//  Created by <PERSON> on 31/05/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXTableView: UXView {
    let backgroundImage = UIImageView()
    let tableView = UITableView()

    override func configure() {
        super.configure()
        
        subviews {
            backgroundImage
            tableView
        }
        
        layout {
            0
            |-0-tableView-0-|
            0
        }

        backgroundImage.setRLMFullScreenImageViewContraints()

        tableView.fillContainer()

        backgroundColor = RLMColor.podcastsSeriesBackground.color
        tableView.backgroundColor = UIColor.clear
        tableView.separatorStyle = .none
    }
    
    override func handleRefresh() {
        super.handleRefresh()
        
        reloadData()
    }
    
    override func setTopAnchorFollowSafeAreaLayoutGuide() {
        tableView.Top == safeAreaLayoutGuide.Top
    }
    
    weak var delegate : UITableViewDelegate? {
        didSet {
            tableView.delegate = delegate
        }
    }

    weak var dataSource : UITableViewDataSource? {
        didSet {
            tableView.dataSource = dataSource
        }
    }
    
    func reloadData(_ animated: Bool = false) {
        if animated {
            DispatchQueue.main.async {
                UIView.transition(
                    with: self.tableView,
                    duration: 0.25,
                    options: .transitionCrossDissolve,
                    animations: {
                        self.tableView.reloadData()
                    }
                )
            }
        } else {
            DispatchQueue.main.async {
                self.tableView.reloadData()
            }
        }
    }

    func reloadRows(_ index: [IndexPath], animated: Bool = false) {
        DispatchQueue.main.async {
            self.tableView.reloadRows(at: index, with: animated ? .fade : .none)
        }
    }
    
    override func setContentInsets(_ insets: UIEdgeInsets) {
        tableView.contentInset = insets
    }
}
