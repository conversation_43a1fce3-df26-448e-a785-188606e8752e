//
//  UXSevenSwitch.swift
//  radio.likemee
//
//  Created by <PERSON> on 07/12/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXSevenSwitch: UXView, UXChoiceView {
    private let textView = UITextView()

    private let rightView = UXView()
    private let sevenSwitch = SevenSwitch()
    
    private(set) var isSelected: Bool = false

    private var _onSelectionChanged: ((_ choiceView: UXChoiceView, _ selected: Bool) -> Void)?
    var deselectOnTap: Bool = false
    
    func onSelectionChanged(
        _ f: @escaping ((_ choiceView: UXChoiceView, _ selected: Bool) -> Void)
    ) {
        sevenSwitch.addTarget(
            self,
            action: #selector(onSwitchValueChanged),
            for: .valueChanged
        )
        _onSelectionChanged = f
    }
    
    @objc private func onSwitchValueChanged(_ sender: SevenSwitch) {
        isSelected = sender.isOn()
        _onSelectionChanged?(self, sender.isOn())
    }

    func setSelected(_ selected: Bool, execute: Bool = false) {
        isSelected = selected
        sevenSwitch.setOn(selected, animated: false, execute: execute)
    }

    convenience init(
        title: String,
        textStyle: UITextStyle,
        linkStyle: UITextStyle? = nil,
        accessibilityIdentifier: String? = nil
    ) {
        self.init(frame: .zero)
        
        textView.contentInset = UIEdgeInsets.zero
        textView.textContainerInset = UIEdgeInsets.zero
        textView.textContainer.lineFragmentPadding = 0

        textView.isScrollEnabled = false
        textView.isEditable = false
        textView.isSelectable = true
        textView.adjustsFontForContentSizeCategory = true
        
        if let linkAttributes = linkStyle?.attributes() {
            textView.linkTextAttributes = linkAttributes
            textView.dataDetectorTypes = .link
            textView.attributedText = NSMutableAttributedString(
                fromHtmlText: title.localized(),
                defaultAttributes: textStyle.attributes(),
                linkAttributes: linkAttributes
            )
        } else {
            textView.attributedText = NSAttributedString(
                string: title.localized(),
                attributes: textStyle.attributes()
            )
        }
        textView.delegate = self
        textView.backgroundColor = UIColor.clear

        rightView.subviews {
            sevenSwitch
        }
        sevenSwitch.top(0).left(0).right(0).width(56).height(28)
        
        subviews {
            rightView
            textView
        }
        
        //align(tops: button, textView)
        align(tops: rightView, textView)
        
        layout (
            0,
            |-0-textView-32-rightView-0-| ~ >=26,
            0
        )

        // set default seveSwitch style
        sevenSwitch.thumbTintColor = RLMColor.switchThumb.color
        sevenSwitch.onThumbTintColor = RLMColor.switchThumb.color
        sevenSwitch.onTintColor = RLMColor.switchActive.color
        sevenSwitch.inactiveColor = RLMColor.switchInactive.color
        sevenSwitch.activeColor = RLMColor.switchActive.color
        sevenSwitch.shadowColor = UIColor.clear
        sevenSwitch.borderColor = UIColor.clear
        sevenSwitch.isRounded = false
        sevenSwitch.accessibilityIdentifier = accessibilityIdentifier
    }
    
    func setSwitchAlpha(_ alpha: CGFloat) {
        sevenSwitch.alpha = alpha
    }
    
    fileprivate var _onLinkTapped: ((_ url: URL) -> Void)?
    
    func onLinkTapped(_ f: @escaping ((_ url: URL) -> Void)) {
        _onLinkTapped = f
    }
}

extension UXSevenSwitch: UITextViewDelegate {
    func textView(
        _ textView: UITextView,
        shouldInteractWith url: URL,
        in characterRange: NSRange,
        interaction: UITextItemInteraction
    ) -> Bool {
        _onLinkTapped?(url)
        return false
    }
}
