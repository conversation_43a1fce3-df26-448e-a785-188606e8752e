//
//  UXLabelWithIcon.swift
//  radio.likemee
//
//  Created by <PERSON> on 24/11/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXLabelWithIcon: UXView, UXChoiceView {
    enum IconPosition {
        case left
        case right
    }

    enum VerticalAlignment {
        case top
        case middle
        case bottom
    }

    private let imageView = UIImageView()
    private let label = UXLabel()
    private let bottomLine = UIView()

    private var spacing = CGFloat(16) /// UXSpacing.spacing_m

    var padding = UIEdgeInsets(top: 0, left: 15, bottom: 13, right: 15) {
        didSet {
            remakeLayout()
        }
    }

    private var valign: VerticalAlignment = .top
    var iconPosition: IconPosition = .right {
        didSet {
            remakeLayout()
        }
    }

    private(set) var isSelected: Bool = false {
        didSet {
            imageView.isHidden = !isSelected
        }
    }

    private var _onSelectionChanged: (
        (_ choiceView: UXChoiceView, _ selected: Bool) -> Void
    )?

    func onSelectionChanged(
        _ f: @escaping ((_ choiceView: UXChoiceView, _ selected: Bool) -> Void)
    ) {
        _onSelectionChanged = f
        imageView.isHidden = !isSelected

        onTap { [weak self] _ in
            guard let this = self else {
                return
            }
            this.setSelected(!this.isSelected, execute: true)
        }
    }

    func setSelected(_ selected: Bool, execute: Bool = false) {
        if isSelected != selected {
            isSelected = selected
            if execute {
                _onSelectionChanged?(self, selected)
            }
        }
    }

    public init(
        title: String? = nil,
        icon: UIImage? = nil,
        iconPosition: IconPosition = .right,
        showBottomLine: Bool = false,
        align: VerticalAlignment = .top,
        padding: UIEdgeInsets? = nil,
        withStyle s: RlmStyle<UXLabel>? = nil
    ) {
        if let padding = padding {
            self.padding = padding
        }
        self.valign = align
        self.iconPosition = iconPosition

        super.init(frame: .zero)

        if let s = s {
            label.style(s)
        }
        label.lineBreakMode = .byTruncatingTail

        if let title = title?.localized() {
            label.setText(title)
        }

        if let icon = icon {
            imageView.image = icon
        }

        self.showBottomLine = showBottomLine
        bottomLine.isHidden = !showBottomLine
    }

    public init(title: String? = nil, withStyle s: RlmStyle<UXLabelWithIcon>) {
        super.init(frame: .zero)

        style(s)

        if let title = title?.localized() {
            label.setText(title)
        }

        bottomLine.isHidden = !showBottomLine
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }

    override func configure() {
        subviews(
            imageView,
            label,
            bottomLine
        )

        remakeLayout()

        bottomLine.backgroundColor = bottomLineColor
        bottomLine.isHidden = !showBottomLine
    }

    private func remakeLayout() {
        removeConstraints(userAddedConstraints)

        if iconPosition == .left {
            imageView.size(24).left(padding.left)

            if valign == .top {
                align(tops: |-padding.left-imageView-spacing-label-padding.right-|)
            } else if valign == .middle {
                imageView.centerVertically()
                align(vertically: |-padding.left-imageView-spacing-label-padding.right-|)
            } else {
                align(bottoms: |-padding.left-imageView-spacing-label-padding.right-|)
            }
        } else {
            imageView.size(24).right(padding.right)

            if valign == .top {
                align(tops: |-padding.left-label-spacing-imageView-padding.right-|)
            } else if valign == .middle {
                imageView.centerVertically()
                align(vertically: |-padding.left-label-spacing-imageView-padding.right-|)
            } else {
                align(bottoms: |-padding.left-label-spacing-imageView-padding.right-|)
            }

        }

        layout {
            padding.top
            label.height(>=24)
            padding.bottom
            |-0-bottomLine-0-| ~ bottomLineHeight
            0
        }
    }

    var labelStyle: RlmStyle<UXLabel>? = nil {
        didSet {
            if let labelStyle = labelStyle {
                label.style(labelStyle)
            }
        }
    }

    var showBottomLine: Bool = true {
        didSet {
            bottomLine.isHidden = !showBottomLine
        }
    }

    var bottomLineColor: UIColor = UIColor.gray {
        didSet {
            bottomLine.backgroundColor = bottomLineColor
        }
    }

    var bottomLineHeight: CGFloat = 1 {
        didSet {
            bottomLine.setHeight(bottomLineHeight)
        }
    }

    var iconColor: UIColor? {
        didSet {
            self.imageView.image = imageView.image?.imageWithColor(color: iconColor)
        }
    }

    var icon: UIImage? {
        set {
            imageView.image = newValue
        }
        get {
            return imageView.image
        }
    }

    var isTapOnImageEnabled = true
    var isImageHidden = false {
        didSet {
            self.imageView.isHidden = isImageHidden
        }
    }

    override var title: String? {
        set {
            self.label.setText(newValue ?? " ")
        }
        get {
            return self.label.getText()
        }
    }

    private var _onImageTap: (() -> Void)?
    private var _tapGesture: UITapGestureRecognizer?

    func onImageTap(_ handler: @escaping (() -> Void)) {
        if _tapGesture == nil {
            isUserInteractionEnabled = true
            _tapGesture = UITapGestureRecognizer(
                target: self,
                action: #selector(onImageTapAction)
            )
            
            if let guesture = _tapGesture {
                self.imageView.addGestureRecognizer(guesture)
                self.imageView.isUserInteractionEnabled = true
            }
        }
        self._onImageTap = handler
    }

    @objc func onImageTapAction(_ sender: UITapGestureRecognizer) {
        if sender.state == .ended && isTapOnImageEnabled {
            self._onImageTap?()
        }
    }
}
