//
//  UXCheckBox.swift
//  radio.likemee
//
//  Created by <PERSON> on 25/11/2020.
//  Copyright © 2020 Computer Rock. All rights reserved.
//

import UIKit
import Stevia

class UXCheckbox: UXView, UXChoiceView, ValidatableField {
    private let textView = UITextView()

    private var selectedImage: UIImage? = nil
    private var deselectedImage: UIImage? = nil
    
    private let leftView = UXView()
    private let button = UXButton()
    
    var isMandatory: Bool = false

    private(set) var isSelected: Bool = false {
        didSet {
            button.setImage(isSelected ? selectedImage : deselectedImage, for: .normal)
        }
    }
    
    private var _onSelectionChanged: ((_ choiceView: UXChoiceView, _ selected: Bool) -> Void)?
    var deselectOnTap: Bool = false
    
    func onSelectionChanged(_ f: @escaping ((_ choiceView: UXChoiceView, _ selected: Bool) -> Void)) {
        _onSelectionChanged = f
    }

    func set<PERSON>elected(_ selected: Bool, execute: Bool = false) {
        if isSelected != selected {
            isSelected = selected
            
            if execute {
                _onSelectionChanged?(self, selected)
            }
        }
    }
    
    func setStyles(
        buttonStyle bs: RlmStyle<UXButton>? = nil,
        textStyle: UITextStyle,
        linkStyle: UITextStyle? = nil
    ) {
        if let linkAttributes = linkStyle?.attributes() {
            textView.linkTextAttributes = linkAttributes
            textView.dataDetectorTypes = .link
            textView.attributedText = NSMutableAttributedString(
                fromHtmlText: self.originalTitle ?? "",
                defaultAttributes: textStyle.attributes(),
                linkAttributes: linkAttributes
            )
        } else {
            textView.attributedText = NSAttributedString(
                string: self.originalTitle ?? "",
                attributes: textStyle.attributes()
            )
        }
        
        if let bs = bs {
            button.style(bs)
            selectedImage = button.image(for: .selected)
            deselectedImage = button.image(for: .normal)
        }
        
        button.isSelected = self.isSelected
    }
    
    private var originalTitle: String?

    convenience init(
        title: String,
        buttonStyle bs: RlmStyle<UXButton>? = nil,
        textStyle: UITextStyle,
        linkStyle: UITextStyle? = nil
    ) {
        self.init(frame: .zero)
        self.originalTitle = title
        textView.textContainerInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        textView.textContainer.lineFragmentPadding = 0
        textView.isScrollEnabled = false
        textView.isEditable = false
        textView.isSelectable = true
        textView.adjustsFontForContentSizeCategory = true
        
        if let linkAttributes = linkStyle?.attributes() {
            textView.linkTextAttributes = linkAttributes
            textView.dataDetectorTypes = .link
            textView.attributedText = NSMutableAttributedString(
                fromHtmlText: title.localized(),
                defaultAttributes: textStyle.attributes(),
                linkAttributes: linkAttributes
            )
        } else {
            textView.attributedText = NSAttributedString(
                string: title.localized(),
                attributes: textStyle.attributes()
            )
        }
        
        textView.delegate = self
        textView.backgroundColor = UIColor.clear

        button.onPress(onButtonTap)
        
        leftView.subviews {
            button
        }
        button.top(0).left(0).right(0)
        
        subviews {
            leftView
            textView
        }
        
        align(tops: leftView, textView)
        
        if let bs = bs {
            button.style(bs)
            selectedImage = button.image(for: .selected)
            deselectedImage = button.image(for: .normal)
            button.size(button.image(for: .normal)?.size.width ?? 26)
            
            layout (
                0,
                |-0-leftView-9-textView-0-| ~ >=26,
                0
            )
        } else {
            layout (
                0,
                |-0-textView-0-| ~ >=26,
                0
            )
        }
    }
    
    private func onButtonTap(sender: UXButton) {
        setSelected(!isSelected, execute: true)
    }
    
    fileprivate var _onLinkTapped: ((_ url: URL) -> Void)?
    
    func onLinkTapped(_ f: @escaping ((_ url: URL) -> Void)) {
        _onLinkTapped = f
    }
    
    private var validator: ((_ checkbox: UXCheckbox, _ value: Bool?) -> ValidationError?)? = nil
    
    var validationErrorMessage: String?
    /// DO NOT CALL FROM VALIDATOR IT SELF
    @discardableResult
    func validate(with validator: @escaping (_ checkbox: UXCheckbox, _ value: Bool?) -> ValidationError?) -> UXCheckbox {
        self.validator = validator
        return self
    }
  
    /// Validate the input and changes the state of the control
    @discardableResult
    func validate() -> Bool {
        guard let error_message = validator?(self, self.isSelected)?.message else {
            return true
        }
        
        validationErrorMessage = error_message
        return false
    }
}

extension UXCheckbox: UITextViewDelegate {
    func textView(
        _ textView: UITextView,
        shouldInteractWith url: URL,
        in characterRange: NSRange,
        interaction: UITextItemInteraction
    ) -> Bool {
        _onLinkTapped?(url)
        return false
    }
}
