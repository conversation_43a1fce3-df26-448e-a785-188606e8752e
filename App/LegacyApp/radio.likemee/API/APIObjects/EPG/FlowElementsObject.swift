//
//  FlowElementsObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 3/30/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class FlowElementsObject: APIObject {
    var entry: [EntryObject] = []

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.entry:
            if let els = value as? [[String: AnyObject]] {
                for el in els {
                    let e = EntryObject()
                    e.setValuesForKeys(el)
                    entry.append(e)
                }
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
