//
//  EntryObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 3/30/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

import Foundation

class EntryObject: APIObject {
    var airtime: Date?
    var entry: [EntryEntryObject] = []
    var type: String?

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.entry:
            if let els = value as? [[String: AnyObject]] {
                for el in els {
                    let e = EntryEntryObject()
                    var entryDict = el
                    entryDict["airtime"] = airtime as AnyObject
                    entryDict["type"] = type as AnyObject
                    e.setValuesForKeys(entryDict)
                    entry.append(e)
                }
            }
        case API.Parameters.airtime:
            if let air = value as? String {
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = Config.epgAirtimeDateFormat
                dateFormatter.timeZone = TimeZone.current
                airtime = dateFormatter.date(from: air)
                for singleEntry in entry {
                    if singleEntry.airtime == nil {
                        singleEntry.updateAirTime(airtime)
                    }
                }
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
