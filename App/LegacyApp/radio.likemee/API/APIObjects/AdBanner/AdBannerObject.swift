//
//  AdBannerObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 7/5/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class AdBannerObject: APIObject {
    var entry: [AdBannerEntry]?

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.entry:
            entry = [AdBannerEntry]()
            if let v = value as? [[String: Any]] {
                for bannerDesc in v {
                    let banner = AdBannerEntry()
                    banner.type = bannerDesc["type"] as? String ?? ""
                    banner.customer = bannerDesc["customer"] as? String ?? ""
                    banner.url = bannerDesc["url"] as? String ?? ""
                    banner.target = bannerDesc["target"] as? String ?? ""
                    if let showValue = bannerDesc["show"] as? String {
                        banner.show = showValue.lowercased() == "true" ? true : false
                    }
                    entry?.append(banner)
                }
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
