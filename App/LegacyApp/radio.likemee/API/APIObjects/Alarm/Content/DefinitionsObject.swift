//
//  DefinitionsObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 4/18/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class DefinitionsObject: APIObject {
    var entry: [DefinitionsEntryObject] = []

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.entry:
            if let els = value as? [[String: AnyObject]] {
                for el in els {
                    let e = DefinitionsEntryObject()
                    e.setValuesForKeys(el)
                    entry.append(e)
                }
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
