//
//  AlarmContentObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 4/18/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class AlarmContentObject: APIObject {
    var alarmContentDescription: String?
    var icon: String?
    var iconActive: String?
    var name: String?
    var definitions: DefinitionsObject?
    var children: ChildrenObject?

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.definitions:
            definitions = DefinitionsObject()
            if let v = value as? [String: Any] {
                definitions?.setValuesForKeys(v)
            }
        case API.Parameters.children:
            children = ChildrenObject()
            if let v = value as? [String: Any] {
                children?.setValuesForKeys(v)
            }
        case API.Parameters.alarmContentDescription:
            if let v = value as? String {
                alarmContentDescription = v
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
