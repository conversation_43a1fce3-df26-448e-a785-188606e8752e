//
//  AlarmContentEntryObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 4/18/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class DefinitionsEntryObject: APIObject {
    var alarmContentId: String?
    var show: Bool?
    var title: String?

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.show:
            if let v = value as? String {
                show = v.toBool()
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
