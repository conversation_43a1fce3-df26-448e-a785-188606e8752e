//
//  DeleteSequenceEntryObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 4/19/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class DeleteSequenceEntryObject: APIObject {
    var status: String?
    var statusCode: String?
    var message: String?
    var sequenceId: String?

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.statusCode:
            if let v = value as? String {
                statusCode = v
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
