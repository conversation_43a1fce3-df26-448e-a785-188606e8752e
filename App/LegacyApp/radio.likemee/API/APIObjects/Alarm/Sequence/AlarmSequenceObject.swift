//
//  AlarmSequenceObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 4/5/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class AlarmSequenceObject: APIObject {
    var delete: DeleteSequenceObject?
    var message: String?
    var playList: String?
    var readyAt: String?
    var sequence: String?
    var sequenceId: String?
    var stationShortname: String?
    var status: String?
    var statusCode: String?

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.delete:
            delete = DeleteSequenceObject()
            if let v = value as? [String: Any] {
                delete?.setValuesForKeys(v)
            }
        case API.Parameters.statusCode:
            if let v = value as? String {
                statusCode = v
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
