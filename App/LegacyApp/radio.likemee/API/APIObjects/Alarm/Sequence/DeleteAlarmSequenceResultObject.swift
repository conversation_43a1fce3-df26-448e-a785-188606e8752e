//
//  DeleteAlarmSequenceResultObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 5/4/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class DeleteAlarmSequenceResultObject: APIObject {
    var entry: [DeleteSequenceEntryObject] = []

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.entry:
            if let els = value as? [[String: AnyObject]] {
                for el in els {
                    let e = DeleteSequenceEntryObject()
                    e.setValuesForKeys(el)
                    entry.append(e)
                }
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
