//
//  AlarmSoundsObject.swift
//  radio.likemee
//
//  Created by <PERSON> on 4/4/17.
//  Copyright © 2017 Computer Rock. All rights reserved.
//

class AlarmSoundsObject: APIObject {
    var title: String?
    var url: String?
    var relativePath: String?

    override func setValue(_ value: Any?, forKey key: String) {
        switch key {
        case API.Parameters.url:
            if let v = value as? String {
                url = v
            }
        default:
            super.setValue(value, forKey: key)
        }
    }
}
