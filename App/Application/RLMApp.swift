//
//  RLMApp.swift
//  RLM
//
//  Created by Computer Rock on 15.10.24..
//

import SwiftUI

@main
struct RLMApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
//    @Environment(\.scenePhase) private var scenePhase
//    let persistenceController = PersistenceController.shared
    
    var body: some Scene {
        WindowGroup {
            SplashViewRepresentable()
                .ignoresSafeArea()
//                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .onOpenURL(perform: { url in
                    UniversalLinks.processLink(url)
                })
                .onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { activity in
                    if let url = activity.webpageURL {
                        UniversalLinks.processLink(url)
                    }
                }
        }
    }
}

struct SplashViewRepresentable: UIViewControllerRepresentable {
    typealias UIViewControllerType = CustomNavigationController

    func makeUIViewController(context: Context) -> CustomNavigationController {
        let storyboard = UIStoryboard(name: "Main", bundle: nil)

        let splash = storyboard.instantiateViewController(identifier: "SplashViewController") { coder in
            return SplashViewController(coder: coder)
        }

        return CustomNavigationController(rootViewController: splash)
    }

    func updateUIViewController(
        _ uiViewController: CustomNavigationController,
        context: Context
    ) { }
}
