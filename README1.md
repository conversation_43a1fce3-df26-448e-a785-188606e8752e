
[![Swift](https://img.shields.io/badge/Swift-5.0-orange?style=flat-square)](https://img.shields.io/badge/Swift-5.0-orange?style=flat-square)
[![Platforms](https://img.shields.io/badge/Platforms-iOS-yellowgreen?style=flat-square)](https://img.shields.io/badge/Platforms-iOS-yellowgreen?style=flat-square)
[![Swift Package Manager](https://img.shields.io/badge/Swift_Package_Manager-compatible-orange?style=flat-square)](https://img.shields.io/badge/Swift_Package_Manager-compatible-orange?style=flat-square)

# Radio Like Me

RadioLikeMe is a mobile application designed to enhance user engagement and streamline interaction with live radio streams. Aimed at radio stations, the app offers a variety of features including live streaming, user reviews, and detailed analytics integration. By utilizing advanced location services, RadioLikeMe provides users with relevant content and updates based on their geographic location, ensuring a personalized and localized listening experience.

The project supports multiple targets, catering to various radio stations with unique requirements. It employs Bitrise for continuous integration and deployment, ensuring a seamless and efficient release pipeline. The app's architecture and codebase are continuously updated to incorporate modern iOS capabilities, enhancing performance and maintaining high standards of quality. RadioLikeMe is dedicated to delivering an exceptional user experience, making it a leading solution in the radio streaming app market.


## Table of Contents
* [Requirements](#requirements)
* [System Setup](#system-setup)
    * [Oh my zsh](#oh-my-zsh)
    * [Homebrew](#homebrew)
    * [Ruby](#ruby)
    * [Python](#python)
    * [Bundler](#bundler)
    * [Node 14](#node)
* [Design Tools](#design-tools)
    * [Linking script](#linking-script)
    * [Runing script](#runing-script)
    * [Troubleshooting](#troubleshooting)
* [CLI Setup](#cli-setup)
    * [Improtant](#improtant)
    * [Environment](#environment-setup)
* [GIT](#git)

## Requirements
- iOS 15.0+
- Xcode
- Node 14

Contact Lasse to invite you to:
- 1PlusX - Private SDK. You need to be invited to this repo in order to fetch it.  - Gitlab (code.the-resc.com) - This is where we have our private forked repos (CRThen, Kommunicate-iOS-SDK, KommunicateChatUI-iOS-SDK)
- Apple developer account
- Bitrise - Client's Bitrise team, not CR one. It's called resc.
- Airship - notification service (for testing)

## System Setup
Prepare your terminal !!!

### Oh my zsh
1. Install Oh-my-zsh
    ```
    sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"
    ```
    **Note:** Your shell is your choice. Setup is pretty much the same. I was writing this as I was going through setup.

2. Restart your terminal

### Homebrew
1. Install Homebrew
   ```
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. After instalation, run the two commands Homebrew is suggesting (make sure you use the correct path, replace computerrock with your username):
    ```
    (echo; echo 'eval "$(/opt/homebrew/bin/brew shellenv)"') >> /Users/<USER>/.zprofile
    ```
    and
    ```
    eval "$(/opt/homebrew/bin/brew shellenv)"
    ```

3. To make sure everything is set correctly, type:
    ```
    brew doctor
    ```
    If everything is ok you'll get the message: 
    ```
    Your system is ready to brew
    ```

### Ruby
1. Install ruby and chruby
    ```
    brew install ruby-install chruby
    ```

2. Verify ruby-install
    ```
    ruby-install -V
    ```
    If everything is ok you'll get the version installed:
    ```
    ruby-install: 0.9.2
    ```

3. Check the current version of Ruby
    ```
    ruby -v
    ```
    It should print something like:
    ```
    ruby 2.6.10p210 (2022-04-12 revision 67958) [universal.arm64e-darwin23]
    ```

4. Now that we have Ruby utilities installed, we can install Ruby. To install the latest Ruby type:
    ```
    ruby-install --update ruby
    ```
    To install specific version of Ruby:
    ```
    ruby-install ruby 3.2.2
    ```

5. Open `/.zshrc` with `open -e /.zshrc`, or by typing `zshconfig` if alias is enabled. Add those lines at bottom of your file.
    ```
    source /opt/homebrew/opt/chruby/share/chruby/chruby.sh
    source /opt/homebrew/opt/chruby/share/chruby/auto.sh
    chruby ruby-3.2.2
    ```

5. Save file and restart your terminal.  
    If something went wrong you should see an error when opening terminal.

6. After restarting the terminal, check the Ruby version again.
    ```
    ruby -v
    ```
    Now it should print:
    ```
    ruby 3.2.2 (2023-03-30 revision e51014f9c0) [arm64-darwin23]
    ```

### Python
1. Install latest version of Python
    ```
    brew install python
    ```

2. Check python version
    ```
    python --version
    ```
    you should get response:
    ```
    command not found: python
    ```

3. Check python info by typing:
    ```
    brew info python
    ```
    You should get a print like this:
    ```
    Python has been installed as
    /opt/homebrew/bin/python3

    Unversioned symlinks `python`, `python-config`, `pip` etc. pointing to `python3`, `python3-config`, `pip3` etc., respectively, have been installed into /opt/homebrew/opt/python@3.11/libexec/bin
    ```
4. Open `/.zshrc` by `open -e /.zshrc`, or by typing `zshconfig` if alias is enabled.

5. Add the following line at the begging of the file:
    ```
    export PATH="/opt/homebrew/opt/python@3.11/libexec/bin:$PATH"
    ```
    This is the path we got from `brew info python`.

6. Check python version:
    ```
    python --version
    ```
    you should get response:
    ```
    Python 3.11.6
    ```

### Bundler
1. Go to your project dir and type:
```
bundle install
```
This will install fastlane and related tools

### Node
1. Install Node 14
    ```
    brew install node@14
    ```
    In case brew tells you that node 14 is no longer support, head to [nodejs.org][nodejs] and download prebuilt installer for version 14.21.3.

3. Configure shell
    ```
    echo 'export PATH="/opt/homebrew/opt/node@14/bin:$PATH"' >> ~/.zshrc
    ```
    **Note:** Follow installation instructions. In case you installed node via prebuilt package, your path might deffer. In that case, node mighe be in `/usr/local/bin/node/bin`.

## Design Tools

Once you pull the project from repo, even when you have all the certificates, profiles and dependecies, you won't be able to build the project. This is where design tools comes in place.

Design tools is a separate project, that's added to iOS project as a submodule. Each target is defined by dedicated Google Spreadsheet. The Spreadsheet contains the general settings and feature flags per app, all strings used in the app as well as the UI specification. Once you run script, it will generate constants, resources, strings, audio files, fonts, add precompiled flags, SPM dependecies, and bunch of other stuff. Once you have this, you can run the project.

More on [WLS Setup][wls-setup]

### Linking script
1. Go to your PROJECT_DIR/design-tools

2. Install node dependencies:
    ```
    npm install
    ```
    in case it doesn't work, try:
    ```
    npm update
    ```
    **Note**: This command might fail due to missing node-canvas dependencies. To install those dependecies do:
    ```
    brew install pkg-config cairo pango libpng jpeg giflib librsvg pixman
    ```
    Refrence: [https://github.com/Automattic/node-canvas#compiling][node-canvas]

3. Link the `generateDesignTokens` script:
    ```
    npm link
    ```

### Runing script
Running the script is done through fastlane. In your PROJECT_DIR type:
```
bundle exec fastlane tokengen --env computerBOB
```
**Note**: Replace `computerBOB` with desired scheme.

### Troubleshooting
In case the script is not getting executed, check the errors you are getting.
If you are geting SSL error, type in console:
```
npm config set strict-ssl false
```
In case you are getting TLS error, add this line to your `/.zshrc`
```
export NODE_TLS_REJECT_UNAUTHORIZED=0
```
If the script has finished, and the folder above is created, you should be able to run the project.

## CLI Setup
There is no white label solution on Bitrise (yet). Each app is on it's own which is kinda troublesome, since all apps have the same setup.

### Improtant
- Only one build can be scheduled. This is due to limitation when reading google sheets in paralel. 
- When adding a new device to Provisioning Profiles, we also need to update Profiles on Bitrise
- Observe builds during `Install node modules and generate design tokens` step. This step doesn't fail if script fails. If you get an error on this step, feel free to abort and restart the build. Error looks something like:  
`(UnhandledPromiseRejectionWarning: Error: Google API error - [429] Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com'`  
It's not a big deal if it's missed, since build will fail on `Xcode archive` step. Downside are time and credits wasted.

### Environment Setup
- PREPROD
- APPSTORE

Our backend has single environment, so Xcode or in this case Bitrise environment plays no role. What is different for these environments, is SDK environment. Some SDKs has single set of keys, while some have dev + prod keys. PREPROD and APPSTORE will always use production keys. For example, Airship has dev + prod keys, while Crashlytics will be the same for all environments.

## GIT

We follow the standard Gitflow workflow to manage our development process. Gitflow is a branching model that helps us keep our project organized and ensures a smooth development lifecycle. 

Key branches in our workflow:
- `master`: The production-ready state of the project.
- `develop`: The branch where the latest development changes are integrated.
- `feature/*`: Feature branches created from `develop` for new features.
- `release/*`: Release branches created from `develop` for preparing a new production release.
- `hotfix/*`: Hotfix branches created from `master` for urgent bug fixes in production.

For a detailed overview of Gitflow, refer to the [Gitflow Cheatsheet][git-flow].

### Versioning

We use Semantic Versioning 2.0.0, following the format `MAJOR.MINOR.PATCH`.

- **Current Version:** X.11.5
- **MAJOR Version:** Increased by 1 for significant changes or incompatible API updates. This version is unique for each app.
- **MINOR Version:** Increased by 1 for backward-compatible functionality additions.
- **PATCH Version:** Increased by 1 for backward-compatible bug fixes. Typically built from the `master` branch.

For more details on Semantic Versioning, refer to the [official documentation][semver].


[nodejs]: https://nodejs.org/en/download/prebuilt-installer
[node-canvas]: https://github.com/Automattic/node-canvas#compiling
[git-flow]: https://danielkummer.github.io/git-flow-cheatsheet/
[semver]: https://semver.org/
[wls-setup]: https://computerrock.atlassian.net/wiki/spaces/RLM/pages/3321462851/WLS+Setup